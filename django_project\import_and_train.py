#!/usr/bin/env python
"""
导入数据并训练模型的脚本
"""
import os
import sys
import django
import pandas as pd

# 设置Django环境
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'django_project.settings')
django.setup()

from coffee.models import CoffeeOrder

def import_data():
    """导入CSV数据到数据库"""
    print("=== 导入咖啡销售数据 ===")
    
    # 检查CSV文件是否存在
    csv_path = '../data/coffee_sales_data.csv'
    if not os.path.exists(csv_path):
        print(f"CSV文件不存在: {csv_path}")
        return False
    
    # 读取CSV数据
    print("读取CSV文件...")
    df = pd.read_csv(csv_path)
    print(f"CSV文件包含 {len(df)} 条记录")
    
    # 清空现有数据
    print("清空现有数据...")
    CoffeeOrder.objects.all().delete()
    
    # 导入数据
    success_count = 0
    error_count = 0
    
    print("开始导入数据...")
    for index, row in df.iterrows():
        try:
            CoffeeOrder.objects.create(
                order_id=row['订单ID'],
                order_date=row['日期'],
                order_time=row['时间'],
                product_name=row['产品名称'],
                quantity=row['数量'],
                original_price=row['原价'],
                paid_amount=row['实付金额'],
                payment_method=row['支付方式'],
                member_level=row['会员等级'],
                discount_rate=row['折扣率']
            )
            success_count += 1
            
            if success_count % 1000 == 0:
                print(f"已导入 {success_count} 条记录...")
                
        except Exception as e:
            error_count += 1
            if error_count <= 5:  # 只显示前5个错误
                print(f"导入第 {index+1} 行数据失败: {e}")
    
    print(f"数据导入完成！成功: {success_count}, 失败: {error_count}")
    return success_count > 0

def train_model():
    """训练预测模型"""
    print("\n=== 训练预测模型 ===")
    
    # 检查数据库中是否有数据
    order_count = CoffeeOrder.objects.count()
    if order_count == 0:
        print("数据库中没有数据，无法训练模型")
        return False
    
    print(f"数据库中有 {order_count} 条订单数据")
    
    # 获取数据并保存为CSV
    print("从数据库获取数据...")
    orders = CoffeeOrder.objects.all().values()
    df = pd.DataFrame(list(orders))
    
    # 保存为训练用的CSV文件
    train_csv_path = 'coffee/模型训练/dataset/train_data.csv'
    os.makedirs(os.path.dirname(train_csv_path), exist_ok=True)
    df.to_csv(train_csv_path, index=False, encoding='utf-8')
    print(f"训练数据已保存到: {train_csv_path}")
    
    # 训练随机森林模型
    print("开始训练随机森林模型...")
    try:
        sys.path.append('coffee/模型训练')
        import model_train

        # 切换到模型训练目录
        original_cwd = os.getcwd()
        os.chdir('coffee/模型训练')

        # 训练随机森林模型
        r2 = model_train.train_model('dataset/train_data.csv')
        print(f"随机森林模型训练完成，R²值: {r2:.4f}")

        # 恢复工作目录
        os.chdir(original_cwd)

        if r2 > 0.3:
            print("随机森林模型训练成功！")
            return True
        else:
            print("随机森林模型质量较低，但仍可使用")
            return True

    except Exception as e:
        print(f"随机森林模型训练失败: {e}")
        import traceback
        traceback.print_exc()
        os.chdir(original_cwd)
        return False

def main():
    """主函数"""
    print("开始数据导入和模型训练流程...")
    
    # 1. 导入数据
    if not import_data():
        print("数据导入失败，退出")
        return
    
    # 2. 训练随机森林模型
    if not train_model():
        print("随机森林模型训练失败，退出")
        return
    
    print("\n=== 流程完成 ===")
    print("现在可以访问以下页面测试预测功能:")
    print("- 原始预测页面: http://localhost:8000/predict/")
    print("- 测试预测页面: http://localhost:8000/test-predict/")

if __name__ == '__main__':
    main()
