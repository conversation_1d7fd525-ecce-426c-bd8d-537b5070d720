from django.urls import path
from django.contrib.auth.decorators import login_required, user_passes_test
from . import views

def is_admin(user):
    return user.is_authenticated and user.is_superuser

urlpatterns = [
    path('', views.index, name='index'),
    path('login/', views.login_view, name='login'),
    path('register/', views.register_view, name='register'),
    path('logout/', views.logout_view, name='logout'),
    path('predict/', views.predict_page, name='predict_page'),
    path('test-predict/', views.test_predict_page, name='test_predict_page'),
    
    # 管理员页面
    path('users/', user_passes_test(is_admin)(views.user_management), name='user_management'),
    path('data/', user_passes_test(is_admin)(views.data_management), name='data_management'),
    path('metrics/', user_passes_test(is_admin)(views.metrics_page), name='metrics_page'),
    
    # 用户管理API
    path('api/users/create/', user_passes_test(is_admin)(views.create_user), name='create_user'),
    path('api/users/<int:user_id>/update/', user_passes_test(is_admin)(views.update_user), name='update_user'),
    path('api/users/<int:user_id>/delete/', user_passes_test(is_admin)(views.delete_user), name='delete_user'),
    
    # 订单管理API
    path('api/orders/create/', user_passes_test(is_admin)(views.create_order), name='create_order'),
    path('api/orders/<str:order_id>/update/', user_passes_test(is_admin)(views.update_order), name='update_order'),
    path('api/orders/<str:order_id>/delete/', user_passes_test(is_admin)(views.delete_order), name='delete_order'),
    
    # API endpoints
    path('api/basic_stats/', views.basic_stats, name='basic_stats'),
    path('api/sales_trend/', views.sales_trend, name='sales_trend'),
    path('api/product_analysis/', views.product_analysis, name='product_analysis'),
    path('api/payment_analysis/', views.payment_analysis, name='payment_analysis'),
    path('api/member_analysis/', views.member_analysis, name='member_analysis'),
    path('api/hourly_sales/', views.hourly_sales, name='hourly_sales'),
    path('api/discount_analysis/', views.discount_analysis, name='discount_analysis'),
    path('api/predict_sales/', views.predict_sales, name='predict_sales'),
    path('api/model_metrics/', user_passes_test(is_admin)(views.model_metrics), name='model_metrics'),
]