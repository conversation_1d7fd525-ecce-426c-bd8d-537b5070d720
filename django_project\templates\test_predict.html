<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>预测测试页面</title>
    <script src="https://cdn.jsdelivr.net/npm/echarts@5.4.3/dist/echarts.min.js"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input, button {
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }
        button {
            background-color: #007bff;
            color: white;
            cursor: pointer;
            border: none;
        }
        button:hover {
            background-color: #0056b3;
        }
        .result {
            margin-top: 20px;
            padding: 15px;
            background-color: #f8f9fa;
            border-radius: 4px;
            display: none;
        }
        .chart-container {
            width: 100%;
            height: 400px;
            margin-top: 20px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        .error {
            color: red;
            background-color: #ffe6e6;
            padding: 10px;
            border-radius: 4px;
            margin-top: 10px;
        }
        .success {
            color: green;
            background-color: #e6ffe6;
            padding: 10px;
            border-radius: 4px;
            margin-top: 10px;
        }
        .debug-info {
            background-color: #f0f0f0;
            padding: 10px;
            border-radius: 4px;
            margin-top: 10px;
            font-family: monospace;
            font-size: 12px;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>咖啡销售预测测试</h1>
        
        <form id="testForm">
            <div class="form-group">
                <label for="date">选择起始日期:</label>
                <input type="date" id="date" name="date" required>
            </div>
            <button type="submit">开始预测</button>
        </form>
        
        <div id="messages"></div>
        
        <div class="result" id="result">
            <h3>预测结果</h3>
            <div class="chart-container">
                <div id="chart"></div>
            </div>
            <div id="predictions"></div>
        </div>
        
        <div id="debugInfo" class="debug-info" style="display: none;"></div>
    </div>

    <script>
        const form = document.getElementById('testForm');
        const messagesDiv = document.getElementById('messages');
        const resultDiv = document.getElementById('result');
        const chartDiv = document.getElementById('chart');
        const predictionsDiv = document.getElementById('predictions');
        const debugDiv = document.getElementById('debugInfo');
        
        let chart = null;
        
        // 设置默认日期为今天
        document.getElementById('date').value = new Date().toISOString().split('T')[0];
        
        function showMessage(message, type = 'info') {
            const div = document.createElement('div');
            div.className = type;
            div.textContent = message;
            messagesDiv.appendChild(div);
            
            // 5秒后自动移除消息
            setTimeout(() => {
                if (div.parentNode) {
                    div.parentNode.removeChild(div);
                }
            }, 5000);
        }
        
        function showDebug(info) {
            debugDiv.style.display = 'block';
            debugDiv.textContent = JSON.stringify(info, null, 2);
        }
        
        function initChart() {
            if (chart) {
                chart.dispose();
            }
            chart = echarts.init(chartDiv);
        }
        
        function getCookie(name) {
            let cookieValue = null;
            if (document.cookie && document.cookie !== '') {
                const cookies = document.cookie.split(';');
                for (let i = 0; i < cookies.length; i++) {
                    const cookie = cookies[i].trim();
                    if (cookie.substring(0, name.length + 1) === (name + '=')) {
                        cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
                        break;
                    }
                }
            }
            return cookieValue;
        }
        
        form.addEventListener('submit', async (e) => {
            e.preventDefault();
            
            const formData = new FormData(form);
            const data = Object.fromEntries(formData.entries());
            
            showMessage('正在发送预测请求...', 'success');
            
            try {
                const response = await fetch('/api/predict_sales/', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRFToken': getCookie('csrftoken')
                    },
                    body: JSON.stringify(data)
                });
                
                const result = await response.json();
                showDebug(result);
                
                if (result.success) {
                    showMessage('预测成功！', 'success');
                    displayResults(result.data.predictions);
                } else {
                    showMessage('预测失败: ' + result.error, 'error');
                }
            } catch (error) {
                showMessage('请求失败: ' + error.message, 'error');
                console.error('Error:', error);
            }
        });
        
        function displayResults(predictions) {
            resultDiv.style.display = 'block';
            
            // 显示预测数据表格
            let tableHtml = '<h4>预测数据</h4><table border="1" style="width:100%; border-collapse: collapse;">';
            tableHtml += '<tr><th>日期</th><th>预测销售额</th><th>下限</th><th>上限</th></tr>';
            
            predictions.forEach(pred => {
                tableHtml += `<tr>
                    <td>${pred.date}</td>
                    <td>¥${pred.amount.toFixed(2)}</td>
                    <td>¥${pred.lower_bound.toFixed(2)}</td>
                    <td>¥${pred.upper_bound.toFixed(2)}</td>
                </tr>`;
            });
            
            tableHtml += '</table>';
            predictionsDiv.innerHTML = tableHtml;
            
            // 绘制图表
            initChart();
            
            const option = {
                title: {
                    text: '未来7天销售额预测',
                    left: 'center'
                },
                tooltip: {
                    trigger: 'axis'
                },
                xAxis: {
                    type: 'category',
                    data: predictions.map(p => p.date)
                },
                yAxis: {
                    type: 'value',
                    name: '销售额 (¥)'
                },
                series: [
                    {
                        name: '预测销售额',
                        type: 'line',
                        data: predictions.map(p => p.amount),
                        lineStyle: { color: '#007bff' },
                        itemStyle: { color: '#007bff' }
                    },
                    {
                        name: '预测上限',
                        type: 'line',
                        data: predictions.map(p => p.upper_bound),
                        lineStyle: { color: '#28a745', type: 'dashed' },
                        itemStyle: { color: '#28a745' }
                    },
                    {
                        name: '预测下限',
                        type: 'line',
                        data: predictions.map(p => p.lower_bound),
                        lineStyle: { color: '#dc3545', type: 'dashed' },
                        itemStyle: { color: '#dc3545' }
                    }
                ],
                legend: {
                    data: ['预测销售额', '预测上限', '预测下限'],
                    top: 30
                }
            };
            
            chart.setOption(option);
            
            // 窗口大小改变时重新调整图表
            window.addEventListener('resize', () => {
                if (chart) {
                    chart.resize();
                }
            });
        }
    </script>
</body>
</html>
