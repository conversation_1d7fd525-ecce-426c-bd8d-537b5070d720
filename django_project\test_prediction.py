#!/usr/bin/env python
"""
测试预测功能的脚本
"""
import os
import sys
import django
from datetime import datetime, timedelta
import json

# 设置Django环境
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'django_project.settings')
django.setup()

from coffee.models import CoffeeOrder
from coffee.views import predict_sales
from django.test import RequestFactory
from django.contrib.auth.models import User

def test_prediction():
    """测试预测功能"""
    print("=== 测试咖啡销售预测功能 ===")
    
    # 1. 检查数据库中是否有数据
    order_count = CoffeeOrder.objects.count()
    print(f"数据库中订单数量: {order_count}")
    
    if order_count > 0:
        # 显示前几条数据
        sample_orders = CoffeeOrder.objects.all()[:3]
        print("\n前3条订单数据:")
        for order in sample_orders:
            print(f"  订单ID: {order.order_id}")
            print(f"  日期: {order.order_date}")
            print(f"  时间: {order.order_time}")
            print(f"  产品: {order.product_name}")
            print(f"  数量: {order.quantity}")
            print(f"  实付金额: {order.paid_amount}")
            print(f"  支付方式: {order.payment_method}")
            print(f"  会员等级: {order.member_level}")
            print(f"  折扣率: {order.discount_rate}")
            print("  ---")
    
    # 2. 检查随机森林模型文件是否存在
    model_path = 'coffee/模型训练/result/random_forest_model.joblib'
    if os.path.exists(model_path):
        print(f"\n随机森林模型文件存在: {model_path}")

        # 尝试加载随机森林模型
        try:
            import joblib
            model, pred_std, label_encoders = joblib.load(model_path)
            print("随机森林模型加载成功")
            print(f"模型类型: {type(model).__name__}")
            print(f"决策树数量: {model.n_estimators}")
            print(f"预测标准差: {pred_std}")
            print(f"标签编码器: {list(label_encoders.keys())}")
        except Exception as e:
            print(f"随机森林模型加载失败: {e}")
            return
    else:
        print(f"\n随机森林模型文件不存在: {model_path}")
        return
    
    # 3. 测试随机森林预测API
    print("\n=== 测试随机森林预测API ===")
    factory = RequestFactory()

    # 创建测试请求
    test_date = datetime.now().strftime('%Y-%m-%d')
    request_data = {'date': test_date}

    request = factory.post('/api/predict_sales/',
                          data=json.dumps(request_data),
                          content_type='application/json')

    # 创建测试用户
    try:
        user = User.objects.get(username='test_user')
    except User.DoesNotExist:
        user = User.objects.create_user(username='test_user', password='test_pass')

    request.user = user

    # 调用随机森林预测函数
    try:
        response = predict_sales(request)
        result = json.loads(response.content)

        print(f"随机森林预测结果: {result}")

        if result.get('success'):
            predictions = result['data']['predictions']
            print(f"\n随机森林预测成功！未来7天预测结果:")
            for pred in predictions:
                print(f"  日期: {pred['date']}")
                print(f"  随机森林预测销售额: ¥{pred['amount']:.2f}")
                print(f"  预测区间: ¥{pred['lower_bound']:.2f} ~ ¥{pred['upper_bound']:.2f}")
                print("  ---")
        else:
            print(f"随机森林预测失败: {result.get('error')}")

    except Exception as e:
        print(f"随机森林预测API调用失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    test_prediction()
