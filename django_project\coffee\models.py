from django.db import models

# Create your models here.

class CoffeeOrder(models.Model):
    """咖啡订单模型
    
    字段说明：
    - order_id: 订单ID，主键
    - order_date: 订单日期
    - order_time: 订单时间
    - product_name: 产品名称
    - quantity: 数量
    - original_price: 原价
    - paid_amount: 实付金额
    - payment_method: 支付方式
    - member_level: 会员等级
    - discount_rate: 折扣率
    - is_verified: 是否验证
    - verification_note: 验证备注
    - verified_by: 验证人
    - verified_at: 验证时间
    """
    
    order_id = models.CharField('订单ID', max_length=50, primary_key=True)
    order_date = models.DateField('订单日期')
    order_time = models.TimeField('订单时间')
    product_name = models.CharField('产品名称', max_length=100)
    quantity = models.IntegerField('数量')
    original_price = models.DecimalField('原价', max_digits=10, decimal_places=2)
    paid_amount = models.DecimalField('实付金额', max_digits=10, decimal_places=2)
    payment_method = models.CharField('支付方式', max_length=50)
    member_level = models.CharField('会员等级', max_length=50)
    discount_rate = models.DecimalField('折扣率', max_digits=4, decimal_places=2)
    
    # 添加订单验证相关字段
    is_verified = models.BooleanField(default=False)
    verification_note = models.TextField(blank=True, null=True)
    verified_by = models.CharField(max_length=100, blank=True, null=True)
    verified_at = models.DateTimeField(blank=True, null=True)

    class Meta:
        verbose_name = '咖啡订单'
        verbose_name_plural = '咖啡订单'
        db_table = 'coffee_order'
        ordering = ['-order_date', '-order_time']

    def __str__(self):
        return f"{self.order_id} - {self.product_name}"
