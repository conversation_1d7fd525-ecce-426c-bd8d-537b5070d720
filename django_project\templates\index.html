<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>咖啡销售数据分析系统</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
    <script src="https://cdn.jsdelivr.net/npm/echarts@5.4.3/dist/echarts.min.js"></script>
    <link href="https://fonts.googleapis.com/css?family=Inter:400,600,700&display=swap" rel="stylesheet">
    <style>
        :root {
            --primary-bg: #181f2a;
            --card-bg: rgba(30, 41, 59, 0.85);
            --accent-1: #00bcd4;
            --accent-2: #ff9800;
            --accent-3: #8e24aa;
            --accent-4: #43a047;
            --accent-5: #f44336;
            --accent-6: #ffd600;
            --text-main: #fff;
            --text-sub: #b0bec5;
            --glass-blur: blur(12px);
        }
        body {
            background: linear-gradient(135deg, #232946 0%, #181f2a 100%);
            color: var(--text-main);
            font-family: 'Inter', 'Roboto', Arial, sans-serif;
            min-height: 100vh;
            margin: 0;
        }
        .navbar {
            position: fixed;
            top: 0; left: 0; right: 0;
            height: 64px;
            background: rgba(24,31,42,0.95);
            box-shadow: 0 4px 24px rgba(0,0,0,0.18);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 40px;
            z-index: 100;
            backdrop-filter: var(--glass-blur);
        }
        .logo {
            font-size: 1.6rem;
            font-weight: 700;
            color: var(--accent-1);
            letter-spacing: 2px;
            display: flex;
            align-items: center;
        }
        .logo i {
            margin-right: 10px;
        }
        .nav-links {
            display: flex;
            gap: 18px;
        }
        .nav-links a {
            color: var(--text-main);
            background: linear-gradient(90deg, var(--accent-1), var(--accent-3));
            padding: 10px 28px;
            border-radius: 18px;
            font-weight: 600;
            text-decoration: none;
            font-size: 1rem;
            transition: background 0.2s, transform 0.2s;
        }
        .nav-links a.danger {
            background: linear-gradient(90deg, var(--accent-5), #b71c1c);
        }
        .nav-links a:hover {
            transform: translateY(-2px) scale(1.05);
            filter: brightness(1.2);
        }
        main {
            margin-top: 90px;
            padding: 0 24px 32px 24px;
            max-width: 1600px;
            margin-left: auto;
            margin-right: auto;
        }
        .stats-row {
            display: flex;
            gap: 112px;
            overflow-x: auto;
            margin-bottom: 36px;
            padding-bottom: 8px;
        }
        .stat-card {
            min-width: 260px;
            background: var(--card-bg);
            border-radius: 24px;
            box-shadow: 0 4px 24px rgba(0,0,0,0.18);
            padding: 32px 28px 24px 28px;
            display: flex;
            flex-direction: column;
            align-items: flex-start;
            position: relative;
            margin-bottom: 0;
            backdrop-filter: var(--glass-blur);
        }
        .stat-icon {
            font-size: 2.2rem;
            margin-bottom: 18px;
            color: var(--accent-1);
            opacity: 0.85;
        }
        .stat-card:nth-child(2) .stat-icon { color: var(--accent-2); }
        .stat-card:nth-child(3) .stat-icon { color: var(--accent-3); }
        .stat-card:nth-child(4) .stat-icon { color: var(--accent-4); }
        .stat-title {
            font-size: 1.1rem;
            color: var(--text-sub);
            margin-bottom: 8px;
            font-weight: 600;
        }
        .stat-value {
            font-size: 2.1rem;
            font-weight: 700;
            color: var(--text-main);
            margin-bottom: 6px;
        }
        .stat-description {
            font-size: 0.95rem;
            color: var(--text-sub);
        }
        .charts-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            grid-template-rows: 340px 340px 340px;
            gap: 32px;
        }
        .chart-card {
            background: var(--card-bg);
            border-radius: 24px;
            box-shadow: 0 4px 24px rgba(0,0,0,0.18);
            padding: 24px 18px 18px 18px;
            position: relative;
            display: flex;
            flex-direction: column;
            justify-content: flex-start;
            backdrop-filter: var(--glass-blur);
        }
        .chart-card h2 {
            font-size: 1.15rem;
            color: var(--accent-1);
            font-weight: 700;
            margin-bottom: 12px;
            letter-spacing: 1px;
        }
        .chart-container {
            flex: 1;
            min-height: 220px;
            height: 100%;
            border-radius: 12px;
            overflow: hidden;
        }
        .refresh-btn {
            float: right;
            padding: 6px 14px;
            background: linear-gradient(90deg, var(--accent-1), var(--accent-2));
            border: none;
            border-radius: 8px;
            color: white;
            cursor: pointer;
            font-size: 13px;
            margin-left: 10px;
            transition: all 0.2s;
        }
        .refresh-btn:hover {
            filter: brightness(1.2);
        }
        .update-time {
            float: right;
            margin-right: 10px;
            font-size: 12px;
            color: var(--text-sub);
        }
        .center-chart {
            display: flex;
            align-items: center;
            justify-content: center;
        }
        @media (max-width: 1200px) {
            .charts-grid {
                grid-template-columns: 1fr 1fr;
                grid-template-rows: repeat(3, 340px);
            }
        }
        @media (max-width: 900px) {
            .charts-grid {
                grid-template-columns: 1fr;
                grid-template-rows: repeat(6, 320px);
            }
            .stats-row {
                flex-direction: column;
                gap: 18px;
            }
        }
        @media (max-width: 600px) {
            main { padding: 0 4px; }
            .stat-card { min-width: 90vw; }
            .charts-grid { gap: 12px; }
        }
    </style>
</head>
<body>
    <div id="globalError" style="display:none;position:fixed;top:70px;left:50%;transform:translateX(-50%);z-index:9999;background:#f44336;color:#fff;padding:12px 32px;border-radius:8px;font-size:1.1rem;box-shadow:0 2px 12px rgba(0,0,0,0.18);font-weight:600;letter-spacing:1px;">数据加载失败</div>
    <nav class="navbar">
        <div class="logo"><i class="fas fa-mug-hot"></i> 基于Python的咖啡销售数据分析系统</div>
        <div class="nav-links">
            {% if user.is_authenticated %}
                <a href="/predict/">销售预测</a>
                <a href="/logout/" class="danger">退出登录</a>
            {% else %}
                <a href="/login/">登录</a>
                <a href="/register/">注册</a>
            {% endif %}
        </div>
    </nav>
    <main>
        <section class="stats-row">
            <div class="stat-card">
                <div class="stat-icon"><i class="fas fa-receipt"></i></div>
                <div class="stat-title">总订单数</div>
                <div class="stat-value" id="totalOrders">-</div>
                <div class="stat-description">累计订单总数</div>
            </div>
            <div class="stat-card">
                <div class="stat-icon"><i class="fas fa-yen-sign"></i></div>
                <div class="stat-title">总销售额</div>
                <div class="stat-value" id="totalSales">-</div>
                <div class="stat-description">累计销售总额（元）</div>
            </div>
            <div class="stat-card">
                <div class="stat-icon"><i class="fas fa-balance-scale"></i></div>
                <div class="stat-title">平均订单金额</div>
                <div class="stat-value" id="avgOrderValue">-</div>
                <div class="stat-description">单笔订单平均金额（元）</div>
            </div>
            <div class="stat-card">
                <div class="stat-icon"><i class="fas fa-coffee"></i></div>
                <div class="stat-title">销售总数量</div>
                <div class="stat-value" id="totalItems">-</div>
                <div class="stat-description">累计销售杯数</div>
            </div>
        </section>
        <section class="charts-grid" style="grid-template-columns: repeat(3, 1fr); grid-template-rows: 340px 340px 340px;">
            <div class="chart-card" style="grid-column: 1 / 4; grid-row: 1 / 2;">
                <h2>Top10产品分析
                    <button id="refreshProduct" class="refresh-btn">
                        <i class="fas fa-sync-alt"></i> 刷新
                    </button>
                    <span class="update-time" id="productUpdateTime"></span>
                </h2>
                <div class="chart-container center-chart" id="productAnalysis"></div>
            </div>
            <div class="chart-card" style="grid-column: 1 / 2; grid-row: 2 / 3;">
                <h2>销售趋势</h2>
                <div class="chart-container" id="salesTrend"></div>
            </div>
            <div class="chart-card" style="grid-column: 2 / 3; grid-row: 2 / 3;">
                <h2>支付方式</h2>
                <div class="chart-container" id="paymentAnalysis"></div>
            </div>
            <div class="chart-card" style="grid-column: 3 / 4; grid-row: 2 / 3;">
                <h2>会员销售额分析</h2>
                <div class="chart-container" id="memberAnalysis"></div>
            </div>
            <div class="chart-card" style="grid-column: 1 / 2; grid-row: 3 / 4;">
                <h2>时段分布</h2>
                <div class="chart-container" id="hourlyAnalysis"></div>
            </div>
            <div class="chart-card" style="grid-column: 2 / 4; grid-row: 3 / 4;">
                <h2>折扣分析</h2>
                <div class="chart-container" id="discountAnalysis"></div>
            </div>
        </section>
    </main>
    <script>
        // 初始化所有图表
        const salesTrendChart = echarts.init(document.getElementById('salesTrend'));
        const productChart = echarts.init(document.getElementById('productAnalysis'));
        const paymentChart = echarts.init(document.getElementById('paymentAnalysis'));
        const memberChart = echarts.init(document.getElementById('memberAnalysis'));
        const hourlyChart = echarts.init(document.getElementById('hourlyAnalysis'));
        const discountChart = echarts.init(document.getElementById('discountAnalysis'));

        // 获取CSS变量
        const style = getComputedStyle(document.documentElement);
        const colors = {
            textMain: style.getPropertyValue('--text-main').trim(),
            textSub: style.getPropertyValue('--text-sub').trim(),
            accent1: style.getPropertyValue('--accent-1').trim(),
            accent2: style.getPropertyValue('--accent-2').trim(),
            accent3: style.getPropertyValue('--accent-3').trim(),
            accent4: style.getPropertyValue('--accent-4').trim(),
            accent5: style.getPropertyValue('--accent-5').trim(),
            accent6: style.getPropertyValue('--accent-6').trim()
        };

        function showGlobalError(msg) {
            const el = document.getElementById('globalError');
            el.textContent = msg || '数据加载失败';
            el.style.display = 'block';
            setTimeout(()=>{el.style.display='none';}, 5000);
        }
        // 加载基础统计数据
        async function loadBasicStats() {
            try {
                const response = await fetch('/api/basic_stats/');
                const data = await response.json();
                console.log('basic_stats', data);
                if (data.success) {
                    document.getElementById('totalOrders').textContent = data.data.total_orders.toLocaleString();
                    document.getElementById('totalSales').textContent = data.data.total_sales.toLocaleString('zh-CN', { minimumFractionDigits: 2, maximumFractionDigits: 2 });
                    document.getElementById('avgOrderValue').textContent = data.data.avg_order_value.toLocaleString('zh-CN', { minimumFractionDigits: 2, maximumFractionDigits: 2 });
                    document.getElementById('totalItems').textContent = data.data.total_items.toLocaleString();
                } else {
                    showGlobalError('基础统计数据加载失败');
                }
            } catch (error) {
                showGlobalError('基础统计数据加载失败');
                console.error('加载基础统计数据失败:', error);
            }
        }

        // 销售趋势：面积图+渐变
        async function loadSalesTrend() {
            try {
                const response = await fetch('/api/sales_trend/');
                const data = await response.json();
                console.log('sales_trend', data);
                if (data.success) {
                    const option = {
                        tooltip: { trigger: 'axis', axisPointer: { type: 'cross' } },
                        legend: { data: ['销售额', '订单数'], textStyle: { color: colors.textMain } },
                        grid: { left: '3%', right: '4%', bottom: '3%', containLabel: true },
                        xAxis: {
                            type: 'category',
                            boundaryGap: false,
                            data: data.data.dates,
                            axisLabel: { color: colors.textSub, rotate: 30 },
                            splitLine: { show: true, lineStyle: { type: 'dashed', color: 'rgba(255,255,255,0.08)' } }
                        },
                        yAxis: [
                            {
                                type: 'value',
                                name: '销售额',
                                axisLabel: { color: colors.textSub, formatter: '{value} 元' },
                                splitLine: { lineStyle: { type: 'dashed', color: 'rgba(255,255,255,0.08)' } }
                            },
                            {
                                type: 'value',
                                name: '订单数',
                                axisLabel: { color: colors.textSub },
                                splitLine: { show: false }
                            }
                        ],
                        series: [
                            {
                                name: '销售额',
                                type: 'line',
                                smooth: true,
                                symbol: 'circle',
                                symbolSize: 8,
                                data: data.data.sales,
                                itemStyle: { color: colors.accent1, borderWidth: 2 },
                                areaStyle: {
                                    color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                                        {offset: 0, color: 'rgba(0,188,212,0.45)'},
                                        {offset: 1, color: 'rgba(0,188,212,0.05)'}
                                    ])
                                },
                                emphasis: { scale: true, focus: 'series' }
                            },
                            {
                                name: '订单数',
                                type: 'bar',
                                yAxisIndex: 1,
                                data: data.data.orders,
                                itemStyle: {
                                    color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                                        {offset: 0, color: colors.accent2},
                                        {offset: 1, color: 'rgba(255,152,0,0.1)'}
                                    ]),
                                    borderRadius: [6, 6, 0, 0]
                                },
                                emphasis: { scale: true, focus: 'series' }
                            }
                        ]
                    };
                    salesTrendChart.setOption(option);
                } else {
                    showGlobalError('销售趋势数据加载失败');
                }
            } catch (error) {
                showGlobalError('销售趋势数据加载失败');
                console.error('加载销售趋势数据失败:', error);
            }
        }

        // Top10产品：环形玫瑰图
        async function loadProductAnalysis(forceUpdate = false) {
            try {
                const btn = document.getElementById('refreshProduct');
                btn.classList.add('loading');
                const url = forceUpdate ? '/api/product_analysis/?force_update=1' : '/api/product_analysis/';
                const response = await fetch(url);
                const data = await response.json();
                console.log('product_analysis', data);
                if (data.success) {
                    document.getElementById('productUpdateTime').textContent = `更新时间: ${data.data.update_time}`;
                    const option = {
                        tooltip: {
                            trigger: 'item',
                            formatter: '{a} <br/>{b}: {c}元 ({d}%)',
                            backgroundColor: 'rgba(30,41,59,0.95)',
                            borderColor: colors.accent1,
                            borderWidth: 1,
                            textStyle: { color: colors.textMain },
                            extraCssText: 'box-shadow: 0 0 8px rgba(0, 188, 212, 0.15);'
                        },
                        legend: {
                            type: 'scroll',
                            orient: 'vertical',
                            right: 10,
                            top: 'middle',
                            itemGap: 10,
                            itemWidth: 10,
                            itemHeight: 10,
                            textStyle: { color: colors.textSub, fontSize: 12 },
                            formatter: (name) => {
                                const value = data.data.sales[data.data.products.indexOf(name)];
                                return `${name.length > 8 ? name.substring(0, 8) + '...' : name}  ¥${value.toFixed(0)}`;
                            }
                        },
                        series: [
                            {
                                name: '产品销售',
                                type: 'pie',
                                roseType: 'radius',
                                radius: ['40%', '70%'],
                                center: ['40%', '50%'],
                                itemStyle: {
                                    borderRadius: 8,
                                    borderColor: '#232946',
                                    borderWidth: 2,
                                    shadowBlur: 12,
                                    shadowColor: 'rgba(0, 188, 212, 0.18)'
                                },
                                label: {
                                    show: true,
                                    position: 'inside',
                                    formatter: '{d}%',
                                    fontSize: 12,
                                    fontWeight: 'bold',
                                    color: '#fff'
                                },
                                labelLine: { show: false },
                                emphasis: {
                                    scale: true,
                                    scaleSize: 10,
                                    itemStyle: {
                                        shadowBlur: 24,
                                        shadowColor: 'rgba(0, 188, 212, 0.35)'
                                    }
                                },
                                data: data.data.products.map((name, index) => ({
                                    name: name,
                                    value: data.data.sales[index],
                                    itemStyle: { color: colors[`accent${(index % 6) + 1}`] }
                                }))
                            }
                        ]
                    };
                    productChart.setOption(option);
                } else {
                    showGlobalError('产品分析数据加载失败');
                }
                btn.classList.remove('loading');
            } catch (error) {
                showGlobalError('产品分析数据加载失败');
                console.error('加载产品分析数据失败:', error);
                document.getElementById('refreshProduct').classList.remove('loading');
            }
        }

        // 支付方式：哑铃分组条形图
        async function loadPaymentAnalysis() {
            try {
                const response = await fetch('/api/payment_analysis/');
                const data = await response.json();
                console.log('payment_analysis', data);
                if (data.success) {
                    const option = {
                        tooltip: {
                            trigger: 'item',
                            formatter: '{a} <br/>{b}: {c}元 ({d}%)',
                            backgroundColor: 'rgba(30,41,59,0.95)',
                            borderColor: colors.accent2,
                            borderWidth: 1,
                            textStyle: { color: colors.textMain }
                        },
                        xAxis: {
                            type: 'category',
                            data: data.data.map(item => item.name),
                            axisLabel: { color: colors.textSub }
                        },
                        yAxis: {
                            type: 'value',
                            axisLabel: { color: colors.textSub }
                        },
                        series: [
                            {
                                name: '支付方式',
                                type: 'bar',
                                data: data.data.map(item => item.amount),
                                itemStyle: {
                                    color: function(params) {
                                        return colors[`accent${(params.dataIndex % 6) + 1}`];
                                    },
                                    borderRadius: [8, 8, 0, 0]
                                },
                                barWidth: 36
                            }
                        ]
                    };
                    paymentChart.setOption(option);
                } else {
                    showGlobalError('支付方式数据加载失败');
                }
            } catch (error) {
                showGlobalError('支付方式数据加载失败');
                console.error('加载支付方式分析数据失败:', error);
            }
        }

        // 会员分析：极坐标柱状图
        async function loadMemberAnalysis() {
            try {
                const response = await fetch('/api/member_analysis/');
                const data = await response.json();
                console.log('member_analysis', data);
                if (data.success) {
                    const option = {
                        angleAxis: {
                            type: 'category',
                            data: data.data.map(item => item.level),
                            axisLabel: { color: colors.textSub }
                        },
                        radiusAxis: {},
                        polar: {},
                        series: [
                            {
                                type: 'bar',
                                data: data.data.map(item => item.sales),
                                coordinateSystem: 'polar',
                                name: '销售额',
                                itemStyle: {
                                    color: colors.accent3,
                                    borderRadius: 8
                                }
                            }
                        ],
                        tooltip: {
                            trigger: 'item',
                            formatter: '{b}: {c}元',
                            backgroundColor: 'rgba(30,41,59,0.95)',
                            borderColor: colors.accent3,
                            borderWidth: 1,
                            textStyle: { color: colors.textMain }
                        }
                    };
                    memberChart.setOption(option);
                } else {
                    showGlobalError('会员分析数据加载失败');
                }
            } catch (error) {
                showGlobalError('会员分析数据加载失败');
                console.error('加载会员分析数据失败:', error);
            }
        }

        // 时段分布：径向热力图
        async function loadHourlyAnalysis() {
            try {
                const response = await fetch('/api/hourly_sales/');
                const data = await response.json();
                console.log('hourly_sales', data);
                if (data.success) {
                    const option = {
                        angleAxis: {
                            type: 'category',
                            data: data.data.hours.map(hour => `${hour}:00`),
                            axisLabel: { color: colors.textSub }
                        },
                        radiusAxis: {},
                        polar: {},
                        series: [
                            {
                                type: 'bar',
                                data: data.data.sales,
                                coordinateSystem: 'polar',
                                name: '销售额',
                                itemStyle: {
                                    color: colors.accent5,
                                    borderRadius: 8
                                }
                            }
                        ],
                        tooltip: {
                            trigger: 'item',
                            formatter: '{b}: {c}元',
                            backgroundColor: 'rgba(30,41,59,0.95)',
                            borderColor: colors.accent5,
                            borderWidth: 1,
                            textStyle: { color: colors.textMain }
                        }
                    };
                    hourlyChart.setOption(option);
                } else {
                    showGlobalError('时段分布数据加载失败');
                }
            } catch (error) {
                showGlobalError('时段分布数据加载失败');
                console.error('加载时段分析数据失败:', error);
            }
        }

        // 折扣分析：堆叠柱状图+折线
        async function loadDiscountAnalysis() {
            try {
                const response = await fetch('/api/discount_analysis/');
                const data = await response.json();
                console.log('discount_analysis', data);
                if (data.success) {
                    const option = {
                        tooltip: {
                            trigger: 'axis',
                            axisPointer: { type: 'cross' },
                            backgroundColor: 'rgba(30,41,59,0.95)',
                            borderColor: colors.accent6,
                            borderWidth: 1,
                            textStyle: { color: colors.textMain },
                            formatter: function(params) {
                                let result = `${params[0].axisValue}<br/>`;
                                params.forEach(param => {
                                    if (param.seriesName === '订单数') {
                                        result += `${param.seriesName}: ${param.value}单<br/>`;
                                    } else if (param.seriesName === '节省金额') {
                                        result += `${param.seriesName}: ¥${param.value.toFixed(2)}<br/>`;
                                    } else {
                                        result += `${param.seriesName}: ${(param.value * 100).toFixed(0)}%<br/>`;
                                    }
                                });
                                return result;
                            }
                        },
                        legend: {
                            data: ['订单数', '节省金额', '折扣率'],
                            textStyle: { color: colors.textSub }
                        },
                        grid: { left: '3%', right: '4%', bottom: '3%', containLabel: true },
                        xAxis: {
                            type: 'category',
                            data: data.data.map(item => `${(item.discount_rate * 100).toFixed(0)}%`),
                            axisLabel: { color: colors.textSub },
                            axisPointer: { type: 'shadow' }
                        },
                        yAxis: [
                            {
                                type: 'value',
                                name: '订单数/节省金额',
                                axisLabel: { color: colors.textSub, formatter: '{value}' }
                            },
                            {
                                type: 'value',
                                name: '折扣率',
                                min: 0,
                                max: 1,
                                interval: 0.1,
                                axisLabel: { color: colors.textSub, formatter: '{value}%' }
                            }
                        ],
                        series: [
                            {
                                name: '订单数',
                                type: 'bar',
                                stack: 'total',
                                data: data.data.map(item => item.order_count),
                                itemStyle: {
                                    color: colors.accent6,
                                    borderRadius: [8, 8, 0, 0]
                                }
                            },
                            {
                                name: '节省金额',
                                type: 'bar',
                                stack: 'total',
                                data: data.data.map(item => item.savings),
                                itemStyle: {
                                    color: colors.accent2,
                                    borderRadius: [8, 8, 0, 0]
                                }
                            },
                            {
                                name: '折扣率',
                                type: 'line',
                                yAxisIndex: 1,
                                smooth: true,
                                symbol: 'circle',
                                symbolSize: 8,
                                data: data.data.map(item => item.discount_rate),
                                itemStyle: { color: colors.accent1 },
                                lineStyle: { width: 3 }
                            }
                        ]
                    };
                    discountChart.setOption(option);
                } else {
                    showGlobalError('折扣分析数据加载失败');
                }
            } catch (error) {
                showGlobalError('折扣分析数据加载失败');
                console.error('加载折扣分析数据失败:', error);
            }
        }

        // 页面加载完成后初始化数据
        document.addEventListener('DOMContentLoaded', () => {
            loadBasicStats();
            loadSalesTrend();
            loadProductAnalysis();
            loadPaymentAnalysis();
            loadMemberAnalysis();
            loadHourlyAnalysis();
            loadDiscountAnalysis();
        });
        window.addEventListener('resize', () => {
            salesTrendChart.resize();
            productChart.resize();
            paymentChart.resize();
            memberChart.resize();
            hourlyChart.resize();
            discountChart.resize();
        });
        document.getElementById('refreshProduct').addEventListener('click', () => {
            loadProductAnalysis(true);
        });
    </script>
</body>
</html>