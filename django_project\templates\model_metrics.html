<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>模型指标 - 咖啡销售预测系统</title>
    <link href="https://cdn.jsdelivr.net/npm/@mdi/font@6.5.95/css/materialdesignicons.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/echarts@5.4.3/dist/echarts.min.js"></script>
    <style>
        :root {
            --primary-color: #6B4423;
            --secondary-color: #8B4513;
            --accent-color: #4CAF50;
            --bg-color: #f5f5f5;
            --card-bg: rgba(255, 255, 255, 0.9);
            --text-primary: #333;
            --text-secondary: #666;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            background: #0f0c29;
            background: linear-gradient(45deg, #24243e, #302b63, #0f0c29);
            color: var(--text-primary);
            font-family: "Microsoft YaHei", Arial, sans-serif;
            min-height: 100vh;
            padding: 30px;
            position: relative;
        }

        body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: 
                radial-gradient(circle at 20% 20%, rgba(139, 69, 19, 0.15) 0%, transparent 30%),
                radial-gradient(circle at 80% 80%, rgba(160, 82, 45, 0.15) 0%, transparent 30%);
            pointer-events: none;
            z-index: -1;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 40px;
            padding: 30px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 20px;
            backdrop-filter: blur(8px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }

        .header-left {
            display: flex;
            align-items: center;
            gap: 20px;
        }

        .header h1 {
            color: #fff;
            font-size: 24px;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
        }

        .nav-links {
            display: flex;
            gap: 15px;
        }

        .nav-links a {
            padding: 10px 20px;
            color: white;
            text-decoration: none;
            border-radius: 8px;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .nav-links a i {
            font-size: 18px;
        }

        .nav-links a.primary {
            background: linear-gradient(135deg, #8b4513, #d2691e);
            box-shadow: 0 4px 15px rgba(139, 69, 19, 0.2);
        }

        .nav-links a.danger {
            background: linear-gradient(135deg, #8b0000, #a52a2a);
            box-shadow: 0 4px 15px rgba(139, 0, 0, 0.2);
        }

        .nav-links a:hover {
            transform: translateY(-2px);
        }

        .metrics-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 30px;
            margin-bottom: 40px;
        }

        .metric-card {
            background: rgba(255, 255, 255, 0.1);
            padding: 30px;
            border-radius: 20px;
            backdrop-filter: blur(8px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s ease;
        }

        .metric-card:hover {
            transform: translateY(-5px);
        }

        .metric-card h2 {
            color: #fff;
            font-size: 20px;
            margin-bottom: 20px;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .metric-card h2 i {
            font-size: 24px;
            color: #4CAF50;
        }

        .metric-content {
            color: #fff;
            font-size: 16px;
            line-height: 1.8;
            white-space: pre-wrap;
            opacity: 0.9;
        }

        .info-item {
            display: flex;
            align-items: center;
            margin-bottom: 15px;
            padding: 10px;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 8px;
        }

        .info-item .label {
            color: rgba(255, 255, 255, 0.7);
            min-width: 120px;
        }

        .info-item .value {
            color: #fff;
            font-weight: 500;
        }

        .feature-importance {
            background: rgba(255, 255, 255, 0.1);
            padding: 30px;
            border-radius: 20px;
            backdrop-filter: blur(8px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            margin-top: 30px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }

        .feature-importance h2 {
            color: #fff;
            font-size: 20px;
            margin-bottom: 20px;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .feature-importance h2 i {
            font-size: 24px;
            color: #2196F3;
        }

        .chart-container {
            background: rgba(255, 255, 255, 0.05);
            border-radius: 15px;
            padding: 20px;
            margin: 30px 0;
            border: 1px solid rgba(255, 255, 255, 0.1);
            height: 400px;
        }

        .feature-table {
            width: 100%;
            border-collapse: separate;
            border-spacing: 0 8px;
        }

        .feature-table th {
            padding: 15px;
            text-align: left;
            color: rgba(255, 255, 255, 0.7);
            font-weight: 600;
            border-bottom: 2px solid rgba(255, 255, 255, 0.1);
        }

        .feature-table td {
            padding: 15px;
            background: rgba(255, 255, 255, 0.05);
            color: #fff;
            transition: all 0.3s ease;
        }

        .feature-table tr:hover td {
            background: rgba(255, 255, 255, 0.1);
            transform: translateX(5px);
        }

        .feature-table tr td:first-child {
            border-top-left-radius: 8px;
            border-bottom-left-radius: 8px;
        }

        .feature-table tr td:last-child {
            border-top-right-radius: 8px;
            border-bottom-right-radius: 8px;
        }

        @media (max-width: 768px) {
            .container {
                padding: 10px;
            }
            
            .header {
                padding: 20px;
                flex-direction: column;
                gap: 20px;
            }

            .nav-links {
                flex-wrap: wrap;
                justify-content: center;
            }

            .metrics-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="header-left">
                <h1>模型指标</h1>
            </div>
            <div class="nav-links">
                <a href="/" class="primary">
                    <i class="mdi mdi-home"></i>返回首页
                </a>
                <a href="/predict/" class="primary">
                    <i class="mdi mdi-chart-line"></i>返回预测
                </a>
                <a href="/users/" class="primary">
                    <i class="mdi mdi-account-group"></i>用户管理
                </a>
                <a href="/data/" class="primary">
                    <i class="mdi mdi-database"></i>数据管理
                </a>
                <a href="/logout/" class="danger">
                    <i class="mdi mdi-logout"></i>退出登录
                </a>
            </div>
        </div>

        <div class="metrics-grid">
            <div class="metric-card">
                <h2><i class="mdi mdi-chart-box"></i>模型评估指标</h2>
                <div class="metric-content">{{ metrics }}</div>
            </div>
            <div class="metric-card">
                <h2><i class="mdi mdi-information"></i>模型信息</h2>
                <div class="metric-content">
                    <div class="info-item">
                        <span class="label">模型类型：</span>
                        <span class="value">线性回归模型</span>
                    </div>
                    <div class="info-item">
                        <span class="label">最后训练时间：</span>
                        <span class="value">{{ last_retrain_time }}</span>
                    </div>
                </div>
            </div>
        </div>

        <div class="feature-importance">
            <h2><i class="mdi mdi-chart-bar"></i>特征重要性</h2>
            <div class="chart-container" id="featureChart"></div>
            <table class="feature-table">
                <thead>
                    <tr>
                        <th>特征</th>
                        <th>重要性</th>
                    </tr>
                </thead>
                <tbody>
                    {% for feature in feature_importance %}
                    <tr>
                        <td>{{ feature.feature }}</td>
                        <td>{{ feature.importance|floatformat:4 }}</td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>

    <script>
        // 初始化特征重要性图表
        const chartContainer = document.getElementById('featureChart');
        const chart = echarts.init(chartContainer);

        const features = {{ feature_importance|safe }};
        const option = {
            title: {
                text: '特征重要性分布',
                left: 'center',
                textStyle: {
                    color: '#fff'
                }
            },
            tooltip: {
                trigger: 'axis',
                axisPointer: {
                    type: 'shadow'
                }
            },
            grid: {
                left: '3%',
                right: '4%',
                bottom: '3%',
                containLabel: true
            },
            xAxis: {
                type: 'value',
                axisLabel: {
                    color: '#fff'
                },
                axisLine: {
                    lineStyle: {
                        color: '#fff'
                    }
                },
                splitLine: {
                    lineStyle: {
                        color: 'rgba(255, 255, 255, 0.1)',
                        type: 'dashed'
                    }
                }
            },
            yAxis: {
                type: 'category',
                data: features.map(f => f.feature),
                axisLabel: {
                    color: '#fff'
                },
                axisLine: {
                    lineStyle: {
                        color: '#fff'
                    }
                }
            },
            series: [
                {
                    name: '重要性',
                    type: 'bar',
                    data: features.map(f => f.importance),
                    itemStyle: {
                        color: new echarts.graphic.LinearGradient(0, 0, 1, 0, [
                            {offset: 0, color: '#4ecdc4'},
                            {offset: 1, color: '#2ecc71'}
                        ]),
                        borderRadius: [0, 4, 4, 0]
                    },
                    emphasis: {
                        itemStyle: {
                            color: new echarts.graphic.LinearGradient(0, 0, 1, 0, [
                                {offset: 0, color: '#3aa1a9'},
                                {offset: 1, color: '#27ae60'}
                            ])
                        }
                    }
                }
            ]
        };

        chart.setOption(option);

        // 监听窗口大小变化
        window.addEventListener('resize', () => {
            chart.resize();
        });
    </script>
</body>
</html> 