<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>管理后台</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: "Microsoft YaHei", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
        }

        :root {
            --primary-color: #4a90e2;
            --success-color: #52c41a;
            --danger-color: #ff4d4f;
            --text-color: #2c3e50;
            --border-color: #e8edf2;
            --hover-color: #f6f9fc;
        }

        body {
            background: linear-gradient(135deg, rgba(246, 249, 252, 0.95) 0%, rgba(237, 242, 247, 0.95) 100%),
                        url('https://cdn.jsdelivr.net/gh/microsoft/vscode/resources/linux/code.png');
            background-size: cover;
            background-position: center;
            background-attachment: fixed;
            color: var(--text-color);
            min-height: 100vh;
            padding: 20px;
            line-height: 1.6;
        }

        .container {
            max-width: 1800px;
            margin: 0 auto;
            padding: 30px;
            background: rgba(255, 255, 255, 0.98);
            border-radius: 16px;
            box-shadow: 0 8px 32px rgba(31, 38, 135, 0.1);
            backdrop-filter: blur(8px);
            position: relative;
        }

        .container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: 
                radial-gradient(circle at 10% 20%, rgba(74, 144, 226, 0.05) 0%, transparent 20%),
                radial-gradient(circle at 90% 80%, rgba(82, 196, 26, 0.05) 0%, transparent 20%);
            pointer-events: none;
            z-index: 0;
        }

        .header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 40px;
            padding-bottom: 20px;
            border-bottom: 2px solid var(--border-color);
            position: relative;
            overflow: hidden;
        }

        .header::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: linear-gradient(
                45deg,
                transparent 0%,
                rgba(74, 144, 226, 0.1) 50%,
                transparent 100%
            );
            animation: shine 10s linear infinite;
            z-index: 0;
        }

        @keyframes shine {
            from {
                transform: rotate(0deg);
            }
            to {
                transform: rotate(360deg);
            }
        }

        .header h1 {
            color: var(--text-color);
            font-size: 32px;
            font-weight: 600;
            background: linear-gradient(120deg, #4a90e2, #67c23a);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
        }

        .nav-links a {
            text-decoration: none;
            color: var(--text-color);
            margin-left: 20px;
            padding: 10px 24px;
            border-radius: 12px;
            transition: all 0.3s ease;
            background: #fff;
            border: 1px solid var(--border-color);
            font-weight: 500;
            display: inline-flex;
            align-items: center;
            box-shadow: 0 2px 6px rgba(0, 0, 0, 0.05);
        }

        .nav-links a:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            border-color: var(--primary-color);
            color: var(--primary-color);
        }

        .section {
            margin-bottom: 40px;
            background: rgba(255, 255, 255, 0.95);
            padding: 30px;
            border-radius: 16px;
            box-shadow: 0 4px 16px rgba(0, 0, 0, 0.05);
            backdrop-filter: blur(8px);
            border: 1px solid rgba(255, 255, 255, 0.5);
            position: relative;
        }

        .section::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(
                45deg,
                transparent 0%,
                rgba(74, 144, 226, 0.05) 50%,
                transparent 100%
            );
            pointer-events: none;
            z-index: 0;
        }

        .section > * {
            position: relative;
            z-index: 1;
        }

        .section h2 {
            color: var(--text-color);
            margin-bottom: 24px;
            padding-bottom: 16px;
            border-bottom: 2px solid var(--border-color);
            font-size: 24px;
            font-weight: 600;
        }

        .section table {
            width: 100%;
            border-collapse: separate;
            border-spacing: 0;
            margin-top: 20px;
            border-radius: 8px;
            overflow: hidden;
        }

        th, td {
            padding: 16px;
            text-align: left;
            border-bottom: 1px solid var(--border-color);
        }

        th {
            background: #f8fafc;
            font-weight: 600;
            color: var(--text-color);
            white-space: nowrap;
        }

        tr:hover {
            background: var(--hover-color);
        }

        .action-btn {
            padding: 8px 16px;
            border-radius: 8px;
            border: none;
            cursor: pointer;
            margin-right: 8px;
            font-size: 14px;
            transition: all 0.3s ease;
            font-weight: 500;
            position: relative;
            z-index: 2;
        }

        .edit-btn {
            background: var(--primary-color);
            color: white;
        }

        .delete-btn {
            background: var(--danger-color);
            color: white;
        }

        .add-btn {
            background: var(--success-color);
            color: white;
            padding: 12px 24px;
            border-radius: 8px;
            margin-bottom: 24px;
            display: inline-flex;
            align-items: center;
            font-weight: 500;
            text-decoration: none;
            box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
            position: relative;
            z-index: 2;
        }

        .action-btn:hover, .add-btn:hover {
            opacity: 0.9;
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        }

        .modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            backdrop-filter: blur(4px);
            justify-content: center;
            align-items: center;
            z-index: 1000;
        }

        .modal-content {
            background: white;
            padding: 40px;
            border-radius: 16px;
            width: 90%;
            max-width: 600px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
            position: relative;
            z-index: 1001;
        }

        .form-group {
            margin-bottom: 24px;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 500;
            color: var(--text-color);
        }

        .form-group input, .form-group select {
            width: 100%;
            padding: 12px;
            border: 1px solid var(--border-color);
            border-radius: 8px;
            font-size: 14px;
            transition: all 0.3s ease;
        }

        .form-group input:focus, .form-group select:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(74, 144, 226, 0.1);
        }

        .close {
            position: absolute;
            right: 20px;
            top: 20px;
            cursor: pointer;
            font-size: 24px;
            color: #999;
            transition: all 0.3s ease;
        }

        .close:hover {
            color: var(--danger-color);
            transform: rotate(90deg);
        }

        .messages {
            margin-bottom: 24px;
        }

        .message {
            padding: 16px 24px;
            border-radius: 8px;
            margin-bottom: 12px;
            font-weight: 500;
        }

        .message.success {
            background: #f6ffed;
            color: var(--success-color);
            border: 1px solid #b7eb8f;
        }

        .message.error {
            background: #fff2f0;
            color: var(--danger-color);
            border: 1px solid #ffccc7;
        }

        .pagination {
            display: flex;
            justify-content: center;
            align-items: center;
            margin-top: 32px;
            gap: 16px;
        }

        .pagination button {
            padding: 10px 20px;
            border: 1px solid var(--border-color);
            background: #fff;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            color: var(--text-color);
            transition: all 0.3s ease;
            font-weight: 500;
        }

        .pagination button:hover:not(:disabled) {
            background: var(--hover-color);
            border-color: var(--primary-color);
            color: var(--primary-color);
            transform: translateY(-2px);
        }

        .pagination button:disabled {
            background: #f5f5f5;
            color: #999;
            cursor: not-allowed;
            border-color: #ddd;
        }

        .pagination span {
            color: var(--text-color);
            font-size: 14px;
            font-weight: 500;
        }

        .model-selector {
            margin-bottom: 24px;
            display: flex;
            align-items: center;
        }

        .model-selector label {
            margin-right: 16px;
            font-weight: 500;
            color: var(--text-color);
        }

        .model-selector select {
            padding: 10px 16px;
            border: 1px solid var(--border-color);
            border-radius: 8px;
            font-size: 14px;
            min-width: 200px;
            background: #fff;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .model-selector select:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(74, 144, 226, 0.1);
        }

        @media (max-width: 768px) {
            .container {
                padding: 20px;
            }

            .header {
                flex-direction: column;
                text-align: center;
            }

            .nav-links {
                margin-top: 20px;
            }

            .nav-links a {
                margin: 5px;
                padding: 8px 16px;
            }

            .section {
                padding: 20px;
            }

            .modal-content {
                padding: 20px;
                width: 95%;
            }
        }

        table {
            position: relative;
            z-index: 1;
        }

        th, td {
            position: relative;
            z-index: 1;
        }

        .nav-links {
            position: relative;
            z-index: 2;
        }

        .header {
            position: relative;
            z-index: 2;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>管理后台</h1>
            <div class="nav-links">
                <a href="/">返回首页</a>
                <a href="/red_book/">营销预测</a>
                <a href="{% url 'logout' %}" style="background: #fff5f5; color: #ff4d4d;">退出登录</a>
            </div>
        </div>

        {% if messages %}
        <div class="messages">
            {% for message in messages %}
            <div class="message {{ message.tags }}">
                {{ message }}
            </div>
            {% endfor %}
        </div>
        {% endif %}

        <div class="section">
            <h2>用户管理</h2>
            <button class="add-btn" onclick="showAddUserModal()">添加用户</button>
            <table>
                <thead>
                    <tr>
                        <th>ID</th>
                        <th>用户名</th>
                        <th>邮箱</th>
                        <th>是否管理员</th>
                        <th>最后登录</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody id="userTableBody">
                </tbody>
            </table>
            <div class="pagination" id="userPagination"></div>
            <div class="pagination-info" id="userPaginationInfo"></div>
        </div>

        <div class="section">
            <h2>数据管理</h2>
            <div class="model-selector">
                <label for="modelSelect">选择数据模型：</label>
                <select id="modelSelect" onchange="changeModel()">
                    <option value="content_keyword">内容关键词</option>
                    <option value="age_distribution">年龄分布</option>
                    <option value="note_data_trend">笔记趋势</option>
                    <option value="fans_demographics">用户画像</option>
                    <option value="fans_interests">用户兴趣</option>
                    <option value="fans_geography">地理分布</option>
                    <option value="fans_active_time">活跃时间</option>
                    <option value="comment_hotwords">评论热词</option>
                </select>
            </div>
            <button class="add-btn" onclick="showAddDataModal()">添加数据</button>
            <div id="tableContainer"></div>
            <div class="pagination" id="dataPagination"></div>
            <div class="pagination-info" id="dataPaginationInfo"></div>
        </div>
    </div>

    <!-- 用户添加/编辑模态框 -->
    <div id="userModal" class="modal">
        <div class="modal-content">
            <span class="close" onclick="closeUserModal()">&times;</span>
            <h2 id="userModalTitle">添加用户</h2>
            <form id="userForm">
                {% csrf_token %}
                <div class="form-group">
                    <label for="username">用户名</label>
                    <input type="text" id="username" name="username" required>
                </div>
                <div class="form-group">
                    <label for="email">邮箱</label>
                    <input type="email" id="email" name="email">
                </div>
                <div class="form-group">
                    <label for="password">密码</label>
                    <input type="password" id="password" name="password">
                </div>
                <div class="form-group">
                    <label>
                        <input type="checkbox" id="is_superuser" name="is_superuser">
                        是否为管理员
                    </label>
                </div>
                <button type="submit" class="action-btn edit-btn">保存</button>
            </form>
        </div>
    </div>

    <!-- 数据添加/编辑模态框 -->
    <div id="dataModal" class="modal">
        <div class="modal-content">
            <span class="close" onclick="closeDataModal()">&times;</span>
            <h2 id="dataModalTitle">添加数据</h2>
            <form id="dataForm">
                {% csrf_token %}
                <div class="form-group">
                    <label for="keyword">关键词</label>
                    <input type="text" id="keyword" name="keyword" required>
                </div>
                <div class="form-group">
                    <label for="note_count">笔记数</label>
                    <input type="number" id="note_count" name="note_count" required>
                </div>
                <div class="form-group">
                    <label for="like_count">点赞数</label>
                    <input type="number" id="like_count" name="like_count" required>
                </div>
                <div class="form-group">
                    <label for="comment_count">评论数</label>
                    <input type="number" id="comment_count" name="comment_count" required>
                </div>
                <div class="form-group">
                    <label for="share_count">分享数</label>
                    <input type="number" id="share_count" name="share_count" required>
                </div>
                <div class="form-group">
                    <label for="collect_count">收藏数</label>
                    <input type="number" id="collect_count" name="collect_count" required>
                </div>
                <div class="form-group">
                    <label for="read_count">阅读数</label>
                    <input type="number" id="read_count" name="read_count" required>
                </div>
                <button type="submit" class="action-btn edit-btn">保存</button>
            </form>
        </div>
    </div>

    <script>
        // 获取CSRF Token
        function getCookie(name) {
            let cookieValue = null;
            if (document.cookie && document.cookie !== '') {
                const cookies = document.cookie.split(';');
                for (let i = 0; i < cookies.length; i++) {
                    const cookie = cookies[i].trim();
                    if (cookie.substring(0, name.length + 1) === (name + '=')) {
                        cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
                        break;
                    }
                }
            }
            return cookieValue;
        }

        // 分页配置
        const PAGE_SIZE = 10;
        let currentUserPage = 1;
        let currentDataPage = 1;

        // 当前选中的模型
        let currentModel = document.getElementById('modelSelect').value;
        
        // 切换数据模型
        function changeModel() {
            const newModel = document.getElementById('modelSelect').value;
            if (newModel !== currentModel) {
                currentModel = newModel;
                currentDataPage = 1;  // 重置页码
                loadData(1);
            }
        }

        // 加载用户数据
        async function loadUsers(page = 1) {
            if (page < 1) return;  // 防止页码小于1
            try {
                const response = await fetch(`/api/admin/users/?page=${page}&page_size=${PAGE_SIZE}`);
                const data = await response.json();
                
                if (data.total_pages > 0 && page > data.total_pages) return;  // 防止页码超出范围
                
                currentUserPage = page;  // 更新当前页码
                
                const tbody = document.getElementById('userTableBody');
                tbody.innerHTML = data.data.map(user => `
                    <tr>
                        <td>${user.id}</td>
                        <td>${user.username}</td>
                        <td>${user.email || ''}</td>
                        <td>${user.is_superuser ? '是' : '否'}</td>
                        <td>${user.last_login || ''}</td>
                        <td>
                            <button class="action-btn edit-btn" onclick="editUser(${user.id})">编辑</button>
                            <button class="action-btn delete-btn" onclick="deleteUser(${user.id})">删除</button>
                        </td>
                    </tr>
                `).join('');

                // 更新分页控件
                const pagination = document.getElementById('userPagination');
                pagination.innerHTML = `
                    <button ${page <= 1 ? 'disabled' : ''} onclick="loadUsers(${page - 1})">上一页</button>
                    <span>第 ${page} 页 / 共 ${data.total_pages} 页</span>
                    <button ${page >= data.total_pages ? 'disabled' : ''} onclick="loadUsers(${page + 1})">下一页</button>
                `;

                document.getElementById('userPaginationInfo').textContent = 
                    `共 ${data.total} 条记录，每页 ${PAGE_SIZE} 条`;
            } catch (error) {
                console.error('Error loading users:', error);
            }
        }

        // 加载数据管理
        async function loadData(page = 1) {
            if (page < 1) return;  // 防止页码小于1
            try {
                const response = await fetch(`/api/admin/data/${currentModel}/?page=${page}&page_size=${PAGE_SIZE}`);
                const data = await response.json();
                
                if (data.total_pages > 0 && page > data.total_pages) return;  // 防止页码超出范围
                
                currentDataPage = page;  // 更新当前页码
                
                // 根据不同模型显示不同的表格结构
                const tableContainer = document.getElementById('tableContainer');
                let tableHTML = '<table><thead><tr>';
                
                // 添加通用列
                tableHTML += '<th>ID</th>';
                
                // 根据模型添加特定列
                switch(currentModel) {
                    case 'content_keyword':
                        tableHTML += `
                            <th>关键词</th>
                            <th>笔记数</th>
                            <th>点赞数</th>
                            <th>评论数</th>
                            <th>分享数</th>
                            <th>收藏数</th>
                            <th>阅读数</th>
                        `;
                        break;
                    case 'age_distribution':
                        tableHTML += `
                            <th>年龄段</th>
                            <th>占比</th>
                        `;
                        break;
                    case 'note_data_trend':
                        tableHTML += `
                            <th>日期</th>
                            <th>笔记数</th>
                            <th>点赞数</th>
                            <th>评论数</th>
                        `;
                        break;
                    case 'fans_demographics':
                        tableHTML += `
                            <th>标签</th>
                            <th>占比</th>
                        `;
                        break;
                    case 'fans_interests':
                        tableHTML += `
                            <th>兴趣</th>
                            <th>粉丝数</th>
                            <th>占比</th>
                        `;
                        break;
                    case 'fans_geography':
                        tableHTML += `
                            <th>省份</th>
                            <th>城市</th>
                            <th>占比</th>
                        `;
                        break;
                    case 'fans_active_time':
                        tableHTML += `
                            <th>时间段</th>
                            <th>占比</th>
                        `;
                        break;
                    case 'comment_hotwords':
                        tableHTML += `
                            <th>关键词</th>
                            <th>频率</th>
                            <th>占比</th>
                        `;
                        break;
                }
                
                tableHTML += '<th>操作</th></tr></thead><tbody>';
                
                // 添加数据行
                data.data.forEach(item => {
                    tableHTML += `<tr><td>${item.id}</td>`;
                    
                    switch(currentModel) {
                        case 'content_keyword':
                            tableHTML += `
                                <td>${item.keyword}</td>
                                <td>${item.note_count}</td>
                                <td>${item.like_count}</td>
                                <td>${item.comment_count}</td>
                                <td>${item.share_count}</td>
                                <td>${item.collect_count}</td>
                                <td>${item.read_count}</td>
                            `;
                            break;
                        case 'age_distribution':
                            tableHTML += `
                                <td>${item.age_range}</td>
                                <td>${item.proportion}</td>
                            `;
                            break;
                        case 'note_data_trend':
                            tableHTML += `
                                <td>${item.date}</td>
                                <td>${item.note_count}</td>
                                <td>${item.like_count}</td>
                                <td>${item.comment_count}</td>
                            `;
                            break;
                        case 'fans_demographics':
                            tableHTML += `
                                <td>${item.tag}</td>
                                <td>${item.proportion}</td>
                            `;
                            break;
                        case 'fans_interests':
                            tableHTML += `
                                <td>${item.interest}</td>
                                <td>${item.fan_count}</td>
                                <td>${item.proportion}</td>
                            `;
                            break;
                        case 'fans_geography':
                            tableHTML += `
                                <td>${item.province}</td>
                                <td>${item.city}</td>
                                <td>${item.proportion}</td>
                            `;
                            break;
                        case 'fans_active_time':
                            tableHTML += `
                                <td>${item.time_period}</td>
                                <td>${item.proportion}</td>
                            `;
                            break;
                        case 'comment_hotwords':
                            tableHTML += `
                                <td>${item.keyword}</td>
                                <td>${item.frequency}</td>
                                <td>${item.proportion}</td>
                            `;
                            break;
                    }
                    
                    tableHTML += `
                        <td>
                            <button class="action-btn edit-btn" onclick="editData(${item.id})">编辑</button>
                            <button class="action-btn delete-btn" onclick="deleteData(${item.id})">删除</button>
                        </td>
                    </tr>`;
                });
                
                tableHTML += '</tbody></table>';
                tableContainer.innerHTML = tableHTML;

                // 更新分页控件
                const pagination = document.getElementById('dataPagination');
                pagination.innerHTML = `
                    <button ${page <= 1 ? 'disabled' : ''} onclick="loadData(${page - 1})">上一页</button>
                    <span>第 ${page} 页 / 共 ${data.total_pages} 页</span>
                    <button ${page >= data.total_pages ? 'disabled' : ''} onclick="loadData(${page + 1})">下一页</button>
                `;

                document.getElementById('dataPaginationInfo').textContent = 
                    `共 ${data.total} 条记录，每页 ${PAGE_SIZE} 条`;
            } catch (error) {
                console.error('Error loading data:', error);
            }
        }

        // 用户管理相关函数
        function showAddUserModal() {
            document.getElementById('userModalTitle').textContent = '添加用户';
            document.getElementById('userForm').reset();
            document.getElementById('userForm').dataset.userId = '';
            document.getElementById('userModal').style.display = 'flex';
        }

        async function editUser(userId) {
            try {
                const response = await fetch(`/api/admin/user/${userId}/`);
                const user = await response.json();
                
                document.getElementById('userModalTitle').textContent = '编辑用户';
                document.getElementById('username').value = user.username;
                document.getElementById('email').value = user.email || '';
                document.getElementById('is_superuser').checked = user.is_superuser;
                document.getElementById('userForm').dataset.userId = userId;
                document.getElementById('userModal').style.display = 'flex';
            } catch (error) {
                console.error('Error loading user:', error);
            }
        }

        async function deleteUser(userId) {
            if (confirm('确定要删除这个用户吗？')) {
                try {
                    const response = await fetch(`/api/admin/user/${userId}/delete/`, {
                        method: 'POST',
                        headers: {
                            'X-CSRFToken': getCookie('csrftoken')
                        }
                    });
                    const result = await response.json();
                    if (result.status === 'success') {
                        loadUsers(currentUserPage);
                    } else {
                        alert('删除失败：' + result.message);
                    }
                } catch (error) {
                    console.error('Error deleting user:', error);
                }
            }
        }

        // 数据管理相关函数
        function showAddDataModal() {
            document.getElementById('dataModalTitle').textContent = '添加数据';
            document.getElementById('dataForm').reset();
            document.getElementById('dataForm').dataset.dataId = '';
            document.getElementById('dataModal').style.display = 'flex';
        }

        async function editData(dataId) {
            try {
                const response = await fetch(`/api/admin/data/${dataId}/get/?model=${currentModel}`);
                if (!response.ok) {
                    const errorData = await response.json();
                    throw new Error(errorData.message || `HTTP error! status: ${response.status}`);
                }
                const data = await response.json();
                
                document.getElementById('dataModalTitle').textContent = '编辑数据';
                
                // 根据不同模型显示不同的表单字段
                const form = document.getElementById('dataForm');
                form.innerHTML = '{% csrf_token %}';  // 重置表单并保留CSRF token
                
                // 根据模型类型创建表单字段
                switch(currentModel) {
                    case 'content_keyword':
                        form.innerHTML += `
                            <div class="form-group">
                                <label for="keyword">关键词</label>
                                <input type="text" id="keyword" name="keyword" value="${data.keyword || ''}" required>
                            </div>
                            <div class="form-group">
                                <label for="note_count">笔记数</label>
                                <input type="number" id="note_count" name="note_count" value="${data.note_count || 0}" required>
                            </div>
                            <div class="form-group">
                                <label for="like_count">点赞数</label>
                                <input type="number" id="like_count" name="like_count" value="${data.like_count || 0}" required>
                            </div>
                            <div class="form-group">
                                <label for="comment_count">评论数</label>
                                <input type="number" id="comment_count" name="comment_count" value="${data.comment_count || 0}" required>
                            </div>
                            <div class="form-group">
                                <label for="share_count">分享数</label>
                                <input type="number" id="share_count" name="share_count" value="${data.share_count || 0}" required>
                            </div>
                            <div class="form-group">
                                <label for="collect_count">收藏数</label>
                                <input type="number" id="collect_count" name="collect_count" value="${data.collect_count || 0}" required>
                            </div>
                            <div class="form-group">
                                <label for="read_count">阅读数</label>
                                <input type="number" id="read_count" name="read_count" value="${data.read_count || 0}" required>
                            </div>
                        `;
                        break;
                    case 'age_distribution':
                        form.innerHTML += `
                            <div class="form-group">
                                <label for="age_range">年龄段</label>
                                <input type="text" id="age_range" name="age_range" value="${data.age_range}" required>
                            </div>
                            <div class="form-group">
                                <label for="proportion">占比</label>
                                <input type="number" id="proportion" name="proportion" value="${data.proportion}" step="0.01" required>
                            </div>
                        `;
                        break;
                    case 'note_data_trend':
                        form.innerHTML += `
                            <div class="form-group">
                                <label for="date">日期</label>
                                <input type="date" id="date" name="date" value="${data.date}" required>
                            </div>
                            <div class="form-group">
                                <label for="note_count">笔记数</label>
                                <input type="number" id="note_count" name="note_count" value="${data.note_count}" required>
                            </div>
                            <div class="form-group">
                                <label for="like_count">点赞数</label>
                                <input type="number" id="like_count" name="like_count" value="${data.like_count}" required>
                            </div>
                            <div class="form-group">
                                <label for="comment_count">评论数</label>
                                <input type="number" id="comment_count" name="comment_count" value="${data.comment_count}" required>
                            </div>
                        `;
                        break;
                    case 'fans_demographics':
                        form.innerHTML += `
                            <div class="form-group">
                                <label for="tag">标签</label>
                                <input type="text" id="tag" name="tag" value="${data.tag}" required>
                            </div>
                            <div class="form-group">
                                <label for="proportion">占比</label>
                                <input type="number" id="proportion" name="proportion" value="${data.proportion}" step="0.01" required>
                            </div>
                        `;
                        break;
                    case 'fans_interests':
                        form.innerHTML += `
                            <div class="form-group">
                                <label for="interest">兴趣</label>
                                <input type="text" id="interest" name="interest" value="${data.interest}" required>
                            </div>
                            <div class="form-group">
                                <label for="fan_count">粉丝数</label>
                                <input type="number" id="fan_count" name="fan_count" value="${data.fan_count}" required>
                            </div>
                            <div class="form-group">
                                <label for="proportion">占比</label>
                                <input type="number" id="proportion" name="proportion" value="${data.proportion}" step="0.01" required>
                            </div>
                        `;
                        break;
                    case 'fans_geography':
                        form.innerHTML += `
                            <div class="form-group">
                                <label for="province">省份</label>
                                <input type="text" id="province" name="province" value="${data.province}" required>
                            </div>
                            <div class="form-group">
                                <label for="city">城市</label>
                                <input type="text" id="city" name="city" value="${data.city}" required>
                            </div>
                            <div class="form-group">
                                <label for="proportion">占比</label>
                                <input type="number" id="proportion" name="proportion" value="${data.proportion}" step="0.01" required>
                            </div>
                        `;
                        break;
                    case 'fans_active_time':
                        form.innerHTML += `
                            <div class="form-group">
                                <label for="time_period">时间段</label>
                                <input type="text" id="time_period" name="time_period" value="${data.time_period}" required>
                            </div>
                            <div class="form-group">
                                <label for="proportion">占比</label>
                                <input type="number" id="proportion" name="proportion" value="${data.proportion}" step="0.01" required>
                            </div>
                        `;
                        break;
                    case 'comment_hotwords':
                        form.innerHTML += `
                            <div class="form-group">
                                <label for="keyword">关键词</label>
                                <input type="text" id="keyword" name="keyword" value="${data.keyword}" required>
                            </div>
                            <div class="form-group">
                                <label for="frequency">频率</label>
                                <input type="number" id="frequency" name="frequency" value="${data.frequency}" required>
                            </div>
                            <div class="form-group">
                                <label for="proportion">占比</label>
                                <input type="number" id="proportion" name="proportion" value="${data.proportion}" step="0.01" required>
                            </div>
                        `;
                        break;
                }
                
                // 添加提交按钮和隐藏的模型字段
                form.innerHTML += `
                    <input type="hidden" name="model" value="${currentModel}">
                    <button type="submit" class="action-btn edit-btn">保存</button>
                `;
                
                form.dataset.dataId = dataId;
                document.getElementById('dataModal').style.display = 'flex';
            } catch (error) {
                console.error('Error loading data:', error);
                alert('加载数据失败：' + error.message);
            }
        }

        async function deleteData(dataId) {
            if (confirm('确定要删除这条数据吗？')) {
                try {
                    const response = await fetch(`/api/admin/data/${dataId}/delete/`, {
                        method: 'POST',
                        headers: {
                            'X-CSRFToken': getCookie('csrftoken')
                        }
                    });
                    const result = await response.json();
                    if (result.status === 'success') {
                        loadData(currentDataPage);
                    } else {
                        alert('删除失败：' + result.message);
                    }
                } catch (error) {
                    console.error('Error deleting data:', error);
                }
            }
        }

        // 表单提交处理
        document.getElementById('userForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            const userId = this.dataset.userId;
            const formData = new FormData(this);
            const url = userId ? 
                `/api/admin/user/${userId}/update/` : 
                '/api/admin/user/create/';
            
            try {
                const response = await fetch(url, {
                    method: 'POST',
                    headers: {
                        'X-CSRFToken': getCookie('csrftoken')
                    },
                    body: formData
                });
                const result = await response.json();
                if (result.status === 'success') {
                    closeUserModal();
                    loadUsers(currentUserPage);
                } else {
                    alert('操作失败：' + result.message);
                }
            } catch (error) {
                console.error('Error:', error);
                alert('操作失败，请检查网络连接');
            }
        });

        async function submitData(event) {
            event.preventDefault();
            const form = event.target;
            const dataId = form.dataset.dataId;
            const formData = new FormData(form);
            const data = {};
            
            // 将FormData转换为普通对象
            for (let [key, value] of formData.entries()) {
                if (key === 'csrfmiddlewaretoken') continue;
                // 对数字类型的字段进行转换
                if (['note_count', 'like_count', 'comment_count', 'share_count', 
                     'collect_count', 'read_count', 'fan_count', 'frequency'].includes(key)) {
                    data[key] = parseInt(value) || 0;
                } else if (key === 'proportion') {
                    data[key] = parseFloat(value) || 0;
                } else {
                    data[key] = value;
                }
            }

            try {
                const url = dataId ? 
                    `/api/admin/data/${dataId}/update/?model=${currentModel}` : 
                    `/api/admin/data/create/?model=${currentModel}`;
                    
                const response = await fetch(url, {
                    method: dataId ? 'PUT' : 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
                    },
                    body: JSON.stringify(data)
                });

                if (!response.ok) {
                    const errorData = await response.json();
                    throw new Error(errorData.message || '提交失败');
                }

                document.getElementById('dataModal').style.display = 'none';
                loadData(currentDataPage);
                alert(dataId ? '更新成功' : '创建成功');
            } catch (error) {
                console.error('Error submitting data:', error);
                alert('提交失败：' + error.message);
            }
        }

        // 关闭模态框
        function closeUserModal() {
            document.getElementById('userModal').style.display = 'none';
        }

        function closeDataModal() {
            document.getElementById('dataModal').style.display = 'none';
        }

        // 初始化加载
        loadUsers();
        loadData();

        // 点击模态框外部关闭
        window.onclick = function(event) {
            if (event.target == document.getElementById('userModal')) {
                closeUserModal();
            }
            if (event.target == document.getElementById('dataModal')) {
                closeDataModal();
            }
        }

        document.addEventListener('DOMContentLoaded', function() {
            // 初始化当前模型
            currentModel = document.getElementById('modelSelect').value;
            
            // 加载初始数据
            loadData(1);
            
            // 监听模型选择变化
            document.getElementById('modelSelect').addEventListener('change', function() {
                currentModel = this.value;
                currentDataPage = 1;
                loadData(1);
            });
            
            // 监听表单提交
            document.getElementById('dataForm').addEventListener('submit', submitData);
            
            // 监听添加数据按钮
            document.getElementById('addDataBtn').addEventListener('click', function() {
                document.getElementById('dataModalTitle').textContent = '添加数据';
                document.getElementById('dataForm').reset();
                document.getElementById('dataForm').dataset.dataId = '';
                document.getElementById('dataModal').style.display = 'flex';
            });
            
            // 监听模态框关闭按钮
            document.querySelector('.close-btn').addEventListener('click', function() {
                document.getElementById('dataModal').style.display = 'none';
            });
        });
    </script>
</body>
</html> 