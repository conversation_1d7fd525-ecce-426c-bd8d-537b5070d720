import os
import sys
import pandas as pd
import django
from datetime import datetime

# 获取项目根目录路径
BASE_DIR = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
# 将项目根目录添加到Python路径
sys.path.append(BASE_DIR)

# 设置Django环境
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'django_project.settings')
django.setup()

from coffee.models import CoffeeOrder

def import_coffee_data():
    """导入咖啡订单数据"""
    print("\n开始导入咖啡订单数据...")

    df = pd.read_csv('./data/coffee_sales_data.csv')
    
    # 清空现有数据
    CoffeeOrder.objects.all().delete()
    success_count = 0
    
    # 遍历数据框的每一行
    for _, row in df.iterrows():
        try:
            # 将日期和时间字符串转换为datetime对象
            date_str = row['日期']
            time_str = row['时间']
            
            CoffeeOrder.objects.create(
                order_id=row['订单ID'],
                order_date=date_str,
                order_time=time_str,
                product_name=row['产品名称'],
                quantity=row['数量'],
                original_price=row['原价'],
                paid_amount=row['实付金额'],
                payment_method=row['支付方式'],
                member_level=row['会员等级'],
                discount_rate=row['折扣率']
            )
            success_count += 1
            print(f"已成功导入订单: {row['订单ID']}")
                
        except Exception as e:
            print(f"导入数据失败: {str(e)}")
            continue
    
    print(f"\n数据导入完成！共成功导入 {success_count} 条咖啡订单数据。")

if __name__ == '__main__':
    import_coffee_data()   # 这里是将数据导入到数据库当中，如果觉得麻烦可以直接导入sql文件