<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>咖啡销售预测</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
    <script src="https://cdn.jsdelivr.net/npm/echarts@5.4.3/dist/echarts.min.js"></script>
    <link href="https://fonts.googleapis.com/css?family=Inter:400,600,700&display=swap" rel="stylesheet">
    <style>
        :root {
            --primary-bg: #181f2a;
            --card-bg: rgba(30, 41, 59, 0.85);
            --accent-1: #00bcd4;
            --accent-2: #ff9800;
            --accent-3: #8e24aa;
            --accent-4: #43a047;
            --accent-5: #f44336;
            --accent-6: #ffd600;
            --text-main: #fff;
            --text-sub: #b0bec5;
            --glass-blur: blur(12px);
        }
        body {
            background: linear-gradient(135deg, #232946 0%, #181f2a 100%);
            color: var(--text-main);
            font-family: 'Inter', 'Roboto', Arial, sans-serif;
            min-height: 100vh;
            margin: 0;
        }
        .navbar {
            position: fixed;
            top: 0; left: 0; right: 0;
            height: 64px;
            background: rgba(24,31,42,0.95);
            box-shadow: 0 4px 24px rgba(0,0,0,0.18);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 40px;
            z-index: 100;
            backdrop-filter: var(--glass-blur);
        }
        .logo {
            font-size: 1.3rem;
            font-weight: 700;
            color: var(--accent-1);
            letter-spacing: 2px;
            display: flex;
            align-items: center;
        }
        .logo i { margin-right: 10px; }
        .nav-links { display: flex; gap: 18px; }
        .nav-links a {
            color: var(--text-main);
            background: linear-gradient(90deg, var(--accent-1), var(--accent-3));
            padding: 10px 28px;
            border-radius: 18px;
            font-weight: 600;
            text-decoration: none;
            font-size: 1rem;
            transition: background 0.2s, transform 0.2s;
        }
        .nav-links a.danger {
            background: linear-gradient(90deg, var(--accent-5), #b71c1c);
        }
        .nav-links a:hover {
            transform: translateY(-2px) scale(1.05);
            filter: brightness(1.2);
        }
        main {
            margin-top: 90px;
            padding: 0 24px 32px 24px;
            max-width: 1200px;
            margin-left: auto;
            margin-right: auto;
        }
        .predict-section {
            display: flex;
            gap: 40px;
            margin-bottom: 36px;
            flex-wrap: wrap;
        }
        .card {
            background: var(--card-bg);
            border-radius: 24px;
            box-shadow: 0 4px 24px rgba(0,0,0,0.18);
            padding: 32px 28px 24px 28px;
            display: flex;
            flex-direction: column;
            align-items: flex-start;
            position: relative;
            margin-bottom: 0;
            backdrop-filter: var(--glass-blur);
            min-width: 320px;
            flex: 1 1 350px;
        }
        .card h2 {
            color: var(--accent-1);
            font-size: 1.2rem;
            font-weight: 700;
            margin-bottom: 18px;
        }
        .chart-section {
            margin-top: 32px;
        }
        .chart-card {
            min-width: 0;
            width: 100%;
            align-items: stretch;
        }
        .chart-container {
            height: 340px;
            border-radius: 12px;
            background: rgba(0,0,0,0.08);
            margin-top: 12px;
        }
        #forecastChart {
            width: 100%;
            height: 100%;
        }
        input, select, textarea {
            background: rgba(30,41,59,0.7);
            color: var(--text-main);
            border: 1.5px solid var(--accent-1);
            border-radius: 8px;
            padding: 10px 16px;
            font-size: 1rem;
            margin-bottom: 16px;
            outline: none;
            transition: border 0.2s, box-shadow 0.2s;
        }
        input:focus, select:focus, textarea:focus {
            border-color: var(--accent-2);
            box-shadow: 0 0 0 2px var(--accent-2);
        }
        button, .btn, .submit-btn {
            background: linear-gradient(90deg, var(--accent-1), var(--accent-2));
            color: #fff;
            border: none;
            border-radius: 8px;
            padding: 10px 28px;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            margin-top: 8px;
            transition: background 0.2s, transform 0.2s;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
        }
        button:hover, .btn:hover, .submit-btn:hover {
            filter: brightness(1.2);
            transform: scale(1.04);
        }
        .result-section {
            background: var(--card-bg);
            padding: 30px;
            border-radius: 20px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
            display: none;
            margin-top: 30px;
            border: 1px solid rgba(0,188,212,0.12);
        }
        .result-section h2 {
            color: var(--accent-1);
            margin-bottom: 30px;
            text-align: center;
            font-size: 24px;
        }
        .prediction-table {
            width: 100%;
            border-collapse: separate;
            border-spacing: 0 8px;
            margin-top: 30px;
        }
        .prediction-table th {
            padding: 15px;
            text-align: left;
            color: var(--text-sub);
            font-weight: 600;
            border-bottom: 2px solid rgba(0,188,212,0.18);
        }
        .prediction-table td {
            padding: 15px;
            background: rgba(0,0,0,0.08);
            color: var(--text-main);
        }
        .prediction-table tr:hover td {
            background: rgba(0,188,212,0.08);
            transform: scale(1.01);
            transition: all 0.3s ease;
        }
        .prediction-table tr td:first-child {
            border-top-left-radius: 8px;
            border-bottom-left-radius: 8px;
        }
        .prediction-table tr td:last-child {
            border-top-right-radius: 8px;
            border-bottom-right-radius: 8px;
        }
        @media (max-width: 900px) {
            .predict-section { flex-direction: column; gap: 18px; }
            main { padding: 0 4px; }
        }
        @media (max-width: 768px) {
            .chart-container { height: 220px; }
            .prediction-table { font-size: 14px; }
            .prediction-table th, .prediction-table td { padding: 10px; }
        }
    </style>
</head>
<body>
    <nav class="navbar">
        <div class="logo"><i class="fas fa-mug-hot"></i> 基于Python的咖啡销售数据分析系统</div>
        <div class="nav-links">
            <a href="/">首页</a>
            {% if user.is_superuser %}
            <a href="/users/">用户管理</a>
            <a href="/data/">数据管理</a>
            <a href="/metrics/">模型指标</a>
            {% endif %}
            <a href="/logout/" class="danger">退出登录</a>
        </div>
    </nav>
    <main>
        <section class="predict-section">
            <div class="card predict-card">
                <h2>销售预测</h2>
                <form id="predictionForm">
                    <div class="form-group">
                        <label>选择起始日期</label>
                        <input type="date" name="date" required>
                    </div>
                    <button type="submit" class="submit-btn">
                        <i class="fas fa-chart-line"></i>
                        预测未来7天销售额
                    </button>
                </form>
            </div>
            <div class="card result-card">
                <h2>预测结果</h2>
                <div class="result-section" id="resultSection">
                    <h2>未来7天销售额预测</h2>
                    <div class="chart-container">
                        <div id="forecastChart"></div>
                    </div>
                    <table class="prediction-table">
                        <thead>
                            <tr>
                                <th>日期</th>
                                <th>预测销售额</th>
                                <th>预测区间下限</th>
                                <th>预测区间上限</th>
                            </tr>
                        </thead>
                        <tbody id="predictionTableBody">
                        </tbody>
                    </table>
                </div>
            </div>
        </section>
    </main>
    <script>
        const form = document.getElementById('predictionForm');
        const resultSection = document.getElementById('resultSection');
        const tableBody = document.getElementById('predictionTableBody');
        const chartContainer = document.getElementById('forecastChart');
        let chart = null;

        // 初始化图表的函数
        function initChart() {
            if (chart) {
                chart.dispose();
            }
            chart = echarts.init(chartContainer);
            console.log('图表初始化成功');
        }
        form.addEventListener('submit', async (e) => {
            e.preventDefault();
            const formData = new FormData(form);
            const data = {};
            for (let [key, value] of formData.entries()) {
                data[key] = value;
            }
            try {
                const response = await fetch('/api/predict_sales/', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRFToken': getCookie('csrftoken')
                    },
                    body: JSON.stringify(data)
                });
                const result = await response.json();
                console.log('API响应:', result);
                if (result.success) {
                    resultSection.style.display = 'block';

                    // 初始化图表
                    initChart();

                    // 更新表格
                    tableBody.innerHTML = '';
                    result.data.predictions.forEach(pred => {
                        tableBody.innerHTML += `
                            <tr>
                                <td>${pred.date}</td>
                                <td>¥${pred.amount.toLocaleString('zh-CN', {minimumFractionDigits: 2, maximumFractionDigits: 2})}</td>
                                <td>¥${pred.lower_bound.toLocaleString('zh-CN', {minimumFractionDigits: 2, maximumFractionDigits: 2})}</td>
                                <td>¥${pred.upper_bound.toLocaleString('zh-CN', {minimumFractionDigits: 2, maximumFractionDigits: 2})}</td>
                            </tr>
                        `;
                    });

                    // 更新图表
                    const option = {
                        title: {
                            text: '未来7天销售额预测',
                            left: 'center',
                            textStyle: {
                                color: '#00bcd4',
                                fontSize: 20,
                                fontWeight: 'bold'
                            }
                        },
                        tooltip: {
                            trigger: 'axis',
                            backgroundColor: 'rgba(30,41,59,0.95)',
                            borderColor: '#00bcd4',
                            borderWidth: 1,
                            textStyle: { color: '#fff' },
                            padding: [10, 15],
                            formatter: function(params) {
                                const pred = params[0];
                                const lower = params[1];
                                const upper = params[2];
                                return `<div style="font-size:14px;padding:8px;">
                                        <div style="margin-bottom:5px;">日期：${pred.name}</div>
                                        <div style="margin-bottom:5px;font-weight:bold;">预测销售额：¥${pred.value.toLocaleString('zh-CN', {minimumFractionDigits: 2, maximumFractionDigits: 2})}</div>
                                        <div>预测区间：</div>
                                        <div>¥${lower.value.toLocaleString('zh-CN', {minimumFractionDigits: 2, maximumFractionDigits: 2})} ~ </div>
                                        <div>¥${upper.value.toLocaleString('zh-CN', {minimumFractionDigits: 2, maximumFractionDigits: 2})}</div>
                                        </div>`;
                            }
                        },
                        grid: {
                            left: '10%',
                            right: '5%',
                            bottom: '15%',
                            top: '15%',
                            containLabel: true
                        },
                        xAxis: {
                            type: 'category',
                            data: result.data.predictions.map(p => p.date),
                            axisLabel: {
                                rotate: 45,
                                color: '#b0bec5',
                                fontSize: 12,
                                margin: 15
                            },
                            axisLine: {
                                lineStyle: {
                                    color: '#00bcd4'
                                }
                            },
                            splitLine: {
                                show: true,
                                lineStyle: {
                                    color: 'rgba(0,188,212,0.08)',
                                    type: 'dashed'
                                }
                            }
                        },
                        yAxis: {
                            type: 'value',
                            name: '销售额',
                            nameTextStyle: {
                                color: '#b0bec5',
                                padding: [0, 0, 0, 40]
                            },
                            axisLabel: {
                                formatter: '¥{value}',
                                color: '#b0bec5',
                                fontSize: 12,
                                margin: 16
                            },
                            splitLine: {
                                lineStyle: {
                                    color: 'rgba(0,188,212,0.08)',
                                    type: 'dashed'
                                }
                            },
                            axisLine: {
                                lineStyle: {
                                    color: '#00bcd4'
                                }
                            },
                            min: function(value) {
                                return Math.floor(value.min * 0.8);
                            },
                            max: function(value) {
                                return Math.ceil(value.max * 1.2);
                            }
                        },
                        series: [
                            {
                                name: '预测销售额',
                                type: 'line',
                                data: result.data.predictions.map(p => p.amount),
                                smooth: true,
                                lineStyle: {
                                    width: 3,
                                    color: '#00bcd4'
                                },
                                itemStyle: {
                                    color: '#00bcd4',
                                    borderWidth: 2,
                                    borderColor: '#fff'
                                },
                                symbol: 'circle',
                                symbolSize: 8,
                                emphasis: {
                                    scale: true,
                                    itemStyle: {
                                        color: '#ff9800'
                                    }
                                },
                                z: 3
                            },
                            {
                                name: '预测区间',
                                type: 'line',
                                data: result.data.predictions.map(p => p.upper_bound),
                                smooth: true,
                                lineStyle: {
                                    width: 0
                                },
                                areaStyle: {
                                    color: {
                                        type: 'linear',
                                        x: 0,
                                        y: 0,
                                        x2: 0,
                                        y2: 1,
                                        colorStops: [{
                                            offset: 0,
                                            color: 'rgba(0,188,212,0.18)'
                                        }, {
                                            offset: 1,
                                            color: 'rgba(0,188,212,0.05)'
                                        }]
                                    }
                                },
                                symbol: 'none',
                                z: 1
                            },
                            {
                                name: '预测区间',
                                type: 'line',
                                data: result.data.predictions.map(p => p.lower_bound),
                                smooth: true,
                                lineStyle: {
                                    width: 0
                                },
                                areaStyle: {
                                    color: {
                                        type: 'linear',
                                        x: 0,
                                        y: 0,
                                        x2: 0,
                                        y2: 1,
                                        colorStops: [{
                                            offset: 0,
                                            color: 'rgba(0,188,212,0.05)'
                                        }, {
                                            offset: 1,
                                            color: 'rgba(0,188,212,0.18)'
                                        }]
                                    }
                                },
                                symbol: 'none',
                                z: 2
                            }
                        ],
                        legend: {
                            data: ['预测销售额', '预测区间'],
                            top: 30,
                            textStyle: {
                                color: '#b0bec5'
                            }
                        },
                        animation: true,
                        animationDuration: 1000,
                        animationEasing: 'cubicOut'
                    };

                    console.log('设置图表选项:', option);
                    chart.setOption(option);

                    // 强制重新渲染图表
                    setTimeout(() => {
                        chart.resize();
                        console.log('图表重新调整大小');
                    }, 100);

                } else {
                    console.error('预测失败:', result.error);
                    alert('预测失败：' + result.error);
                }
            } catch (error) {
                console.error('请求失败:', error);
                alert('请求失败：' + error.message);
            }
        });
        function getCookie(name) {
            let cookieValue = null;
            if (document.cookie && document.cookie !== '') {
                const cookies = document.cookie.split(';');
                for (let i = 0; i < cookies.length; i++) {
                    const cookie = cookies[i].trim();
                    if (cookie.substring(0, name.length + 1) === (name + '=')) {
                        cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
                        break;
                    }
                }
            }
            return cookieValue;
        }
        window.addEventListener('resize', () => {
            if(chart) {
                chart.resize({ width: 'auto', height: 'auto' });
            }
        });
        const observer = new MutationObserver((mutations) => {
            mutations.forEach((mutation) => {
                if (mutation.target.style.display === 'block') {
                    chart.resize();
                }
            });
        });
        observer.observe(resultSection, {
            attributes: true,
            attributeFilter: ['style']
        });
    </script>
</body>
</html> 