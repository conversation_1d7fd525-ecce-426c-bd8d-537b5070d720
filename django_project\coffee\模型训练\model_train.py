import pandas as pd
import numpy as np
from sklearn.ensemble import RandomForestRegressor
from sklearn.preprocessing import LabelEncoder
from sklearn.metrics import mean_squared_error, r2_score, mean_absolute_error
import matplotlib.pyplot as plt
import seaborn as sns
from datetime import datetime, timedelta
import os
try:
    import joblib
except ImportError:
    try:
        from sklearn.externals import joblib
    except ImportError:
        import pickle as joblib

plt.rcParams['font.sans-serif'] = ['SimHei']  # 指定默认字体为黑体
plt.rcParams['axes.unicode_minus'] = False  # 正确显示负号

def create_features(df):
    """创建特征"""
    # 时间特征
    if '时间' in df.columns:
        df['hour'] = pd.to_datetime(df['时间']).dt.hour
    elif 'order_time' in df.columns:
        # 如果是从数据库来的数据，order_time可能是time对象
        df['hour'] = df['order_time'].apply(lambda x: x.hour if hasattr(x, 'hour') else
                                           pd.to_datetime(str(x)).hour)
    else:
        df['hour'] = 12  # 默认值

    df['dayofweek'] = df['order_date'].dt.dayofweek
    df['quarter'] = df['order_date'].dt.quarter
    df['month'] = df['order_date'].dt.month
    df['year'] = df['order_date'].dt.year

    # 编码分类特征 - 处理中文和英文列名
    label_encoders = {}

    # 列名映射
    column_mapping = {
        '支付方式': 'payment_method',
        '会员等级': 'member_level',
        '产品名称': 'product_name'
    }

    for chinese_name, english_name in column_mapping.items():
        # 优先使用中文列名，如果不存在则使用英文列名
        if chinese_name in df.columns:
            column_name = chinese_name
        elif english_name in df.columns:
            column_name = english_name
            # 创建中文列名的副本以保持一致性
            df[chinese_name] = df[english_name]
        else:
            print(f'警告: 找不到列 {chinese_name} 或 {english_name}')
            continue

        label_encoders[chinese_name] = LabelEncoder()
        df[f'{chinese_name}_encoded'] = label_encoders[chinese_name].fit_transform(df[column_name])

    return df, label_encoders

def train_model(data_path):
    """训练随机森林回归模型进行时序预测"""
    # 创建result文件夹（如果不存在）
    if not os.path.exists('result'):
        os.makedirs('result')

    # 读取数据
    data = pd.read_csv(data_path)
    print(f'读取数据成功，共 {len(data)} 条记录')

    # 将日期列转换为datetime类型
    data['order_date'] = pd.to_datetime(data['日期'])

    # 创建特征
    data, label_encoders = create_features(data)
    print('特征创建完成')

    # 按日期汇总并计算特征均值
    # 处理列名映射
    quantity_col = '数量' if '数量' in data.columns else 'quantity'
    discount_col = '折扣率' if '折扣率' in data.columns else 'discount_rate'
    amount_col = '实付金额' if '实付金额' in data.columns else 'paid_amount'

    agg_dict = {
        'hour': 'mean',
        'dayofweek': 'first',
        'quarter': 'first',
        'month': 'first',
        'year': 'first',
        '支付方式_encoded': 'mean',
        '会员等级_encoded': 'mean',
        '产品名称_encoded': 'mean',
        quantity_col: 'mean',
        discount_col: 'mean',
        amount_col: 'sum'
    }

    daily_features = data.groupby('order_date').agg(agg_dict).reset_index()
    print(f'按日期汇总完成，共 {len(daily_features)} 天的数据')

    # 统一列名
    if quantity_col != '数量':
        daily_features['数量'] = daily_features[quantity_col]
    if discount_col != '折扣率':
        daily_features['折扣率'] = daily_features[discount_col]
    if amount_col != '实付金额':
        daily_features['实付金额'] = daily_features[amount_col]

    # 准备特征和目标变量
    feature_columns = ['hour', 'dayofweek', 'quarter', 'month', 'year',
                      '支付方式_encoded', '会员等级_encoded', '产品名称_encoded',
                      '数量', '折扣率']

    X = daily_features[feature_columns]
    y = daily_features['实付金额']
    print(f'特征矩阵形状: {X.shape}，目标变量长度: {len(y)}')

    # 创建并训练随机森林回归模型
    model = RandomForestRegressor(
        n_estimators=100,      # 决策树数量
        max_depth=10,          # 最大深度
        min_samples_split=5,   # 内部节点再划分所需最小样本数
        min_samples_leaf=2,    # 叶子节点最少样本数
        random_state=42,       # 随机种子
        n_jobs=-1             # 使用所有CPU核心
    )
    print('开始训练随机森林模型...')
    model.fit(X, y)
    print('模型训练完成')
    
    # 预测训练集
    y_pred = model.predict(X)
    print('训练集预测完成')

    # 计算评估指标
    mse = mean_squared_error(y, y_pred)
    rmse = np.sqrt(mse)
    mae = mean_absolute_error(y, y_pred)
    r2 = r2_score(y, y_pred)
    print(f'模型评估指标 - R²: {r2:.4f}, RMSE: {rmse:.4f}, MAE: {mae:.4f}')

    # 获取特征重要性
    feature_importance = pd.DataFrame({
        'feature': feature_columns,
        'importance': model.feature_importances_
    }).sort_values('importance', ascending=False)
    print('特征重要性排序:')
    for _, row in feature_importance.iterrows():
        print(f'  {row["feature"]}: {row["importance"]:.4f}')

    # 预测未来7天
    last_date = data['order_date'].max()
    future_dates = pd.date_range(start=last_date + timedelta(days=1), periods=7, freq='D')
    print(f'开始预测未来7天 ({future_dates[0].strftime("%Y-%m-%d")} 到 {future_dates[-1].strftime("%Y-%m-%d")})')

    # 创建未来数据的特征
    future_df = pd.DataFrame({'order_date': future_dates})
    future_df['hour'] = data['hour'].mean()
    future_df['dayofweek'] = future_df['order_date'].dt.dayofweek
    future_df['quarter'] = future_df['order_date'].dt.quarter
    future_df['month'] = future_df['order_date'].dt.month
    future_df['year'] = future_df['order_date'].dt.year
    future_df['支付方式_encoded'] = data['支付方式_encoded'].mean()
    future_df['会员等级_encoded'] = data['会员等级_encoded'].mean()
    future_df['产品名称_encoded'] = data['产品名称_encoded'].mean()
    future_df['数量'] = data['数量'].mean()
    future_df['折扣率'] = data['折扣率'].mean()

    # 使用模型预测
    future_X = future_df[feature_columns]
    future_pred = model.predict(future_X)
    print('未来7天预测完成')

    # 计算预测区间（使用随机森林的预测不确定性）
    # 对于随机森林，我们可以使用各个树的预测结果来估计不确定性
    tree_predictions = np.array([tree.predict(future_X) for tree in model.estimators_])
    pred_std = np.std(tree_predictions, axis=0)

    future_df['yhat'] = future_pred
    future_df['yhat_lower'] = future_pred - 1.96 * pred_std
    future_df['yhat_upper'] = future_pred + 1.96 * pred_std
    
    # 绘制预测结果图
    plt.figure(figsize=(12, 8))

    # 子图1: 预测结果
    plt.subplot(2, 1, 1)
    plt.plot(daily_features['order_date'], daily_features['实付金额'], label='实际销售额', marker='o', markersize=3)
    plt.plot(future_dates, future_pred, label='随机森林预测销售额', color='red', marker='s', markersize=4)
    plt.fill_between(future_dates,
                     future_df['yhat_lower'],
                     future_df['yhat_upper'],
                     color='red',
                     alpha=0.2,
                     label='预测区间')
    plt.title('随机森林销售额预测')
    plt.xlabel('日期')
    plt.ylabel('销售额')
    plt.legend()
    plt.grid(True, alpha=0.3)

    # 子图2: 特征重要性
    plt.subplot(2, 1, 2)
    plt.barh(feature_importance['feature'], feature_importance['importance'])
    plt.title('随机森林特征重要性')
    plt.xlabel('重要性')
    plt.tight_layout()
    plt.savefig('result/forecast_plot.png', dpi=300, bbox_inches='tight')
    plt.close()
    print('预测图表已保存')

    # 保存模型评估指标
    with open('result/model_evaluation.txt', 'w', encoding='utf-8') as f:
        f.write('=== 随机森林回归模型评估报告 ===\n\n')
        f.write(f'均方误差 (MSE): {mse:.4f}\n')
        f.write(f'均方根误差 (RMSE): {rmse:.4f}\n')
        f.write(f'平均绝对误差 (MAE): {mae:.4f}\n')
        f.write(f'决定系数 (R²): {r2:.4f}\n\n')
        f.write('=== 模型参数 ===\n')
        f.write(f'决策树数量: {model.n_estimators}\n')
        f.write(f'最大深度: {model.max_depth}\n')
        f.write(f'最小分割样本数: {model.min_samples_split}\n')
        f.write(f'叶子节点最小样本数: {model.min_samples_leaf}\n\n')
        f.write('=== 特征重要性排序 ===\n')
        for _, row in feature_importance.iterrows():
            f.write(f'{row["feature"]}: {row["importance"]:.4f}\n')

    # 保存模型和编码器
    joblib.dump((model, pred_std, label_encoders), 'result/random_forest_model.joblib')
    print('随机森林模型已保存')

    # 保存最近的预测结果
    future_df.to_csv('result/latest_forecast.csv', index=False)
    print('预测结果已保存')

    return r2

if __name__ == '__main__':
    train_model('dataset/coffee_sales_data.csv')  # 这里是模型训练模块，训练结果保存在result文件夹当中