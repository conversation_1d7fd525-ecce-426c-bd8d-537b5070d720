# Generated by Django 3.2.25 on 2025-03-29 03:02

from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='CoffeeOrder',
            fields=[
                ('order_id', models.CharField(max_length=20, primary_key=True, serialize=False, verbose_name='订单ID')),
                ('order_date', models.DateField(verbose_name='订单日期')),
                ('order_time', models.TimeField(verbose_name='订单时间')),
                ('product_name', models.CharField(max_length=50, verbose_name='产品名称')),
                ('quantity', models.IntegerField(verbose_name='数量')),
                ('original_price', models.DecimalField(decimal_places=2, max_digits=10, verbose_name='原价')),
                ('paid_amount', models.DecimalField(decimal_places=2, max_digits=10, verbose_name='实付金额')),
                ('payment_method', models.Char<PERSON>ield(max_length=20, verbose_name='支付方式')),
                ('member_level', models.CharField(max_length=20, verbose_name='会员等级')),
                ('discount_rate', models.DecimalField(decimal_places=2, max_digits=3, verbose_name='折扣率')),
            ],
            options={
                'verbose_name': '咖啡订单',
                'verbose_name_plural': '咖啡订单',
                'db_table': 'coffee_order',
            },
        ),
    ]
