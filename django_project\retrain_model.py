#!/usr/bin/env python
"""
重新训练模型的脚本
"""
import os
import sys
import django
from datetime import datetime, timedelta
import pandas as pd

# 设置Django环境
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'django_project.settings')
django.setup()

from coffee.models import CoffeeOrder

def retrain_model():
    """重新训练模型"""
    print("=== 重新训练咖啡销售预测模型 ===")
    
    # 1. 检查数据库中是否有数据
    order_count = CoffeeOrder.objects.count()
    print(f"数据库中订单数量: {order_count}")
    
    if order_count == 0:
        print("数据库中没有数据，请先导入数据")
        return
    
    # 2. 获取所有数据
    print("获取数据库中的所有订单数据...")
    orders = CoffeeOrder.objects.all().values()
    df = pd.DataFrame(list(orders))
    
    print(f"获取到 {len(df)} 条订单数据")
    print("数据列名:", df.columns.tolist())
    
    # 3. 保存为CSV文件供模型训练使用
    csv_path = 'coffee/模型训练/dataset/database_coffee_sales_data.csv'
    df.to_csv(csv_path, index=False, encoding='utf-8')
    print(f"数据已保存到: {csv_path}")
    
    # 4. 导入并运行随机森林模型训练
    print("开始训练随机森林模型...")
    sys.path.append('coffee/模型训练')
    import model_train

    try:
        # 切换到模型训练目录
        original_cwd = os.getcwd()
        os.chdir('coffee/模型训练')

        # 训练随机森林模型
        r2 = model_train.train_model('dataset/database_coffee_sales_data.csv')
        print(f"随机森林模型训练完成，R²值: {r2:.4f}")

        # 恢复工作目录
        os.chdir(original_cwd)

        if r2 > 0.5:
            print("随机森林模型训练成功！")
        else:
            print("随机森林模型质量较低，可能需要更多数据或特征工程")
            
    except Exception as e:
        print(f"模型训练失败: {e}")
        import traceback
        traceback.print_exc()
        os.chdir(original_cwd)

if __name__ == '__main__':
    retrain_model()
