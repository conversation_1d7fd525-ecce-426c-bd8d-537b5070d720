<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>用户管理 - 咖啡销售预测系统</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
    <link href="https://fonts.googleapis.com/css?family=Inter:400,600,700&display=swap" rel="stylesheet">
    <style>
        :root {
            --primary-bg: #181f2a;
            --card-bg: rgba(30, 41, 59, 0.85);
            --accent-1: #00bcd4;
            --accent-2: #ff9800;
            --accent-3: #8e24aa;
            --accent-4: #43a047;
            --accent-5: #f44336;
            --accent-6: #ffd600;
            --text-main: #fff;
            --text-sub: #b0bec5;
            --glass-blur: blur(12px);
        }
        body {
            background: linear-gradient(135deg, #232946 0%, #181f2a 100%);
            color: var(--text-main);
            font-family: 'Inter', 'Roboto', Arial, sans-serif;
            min-height: 100vh;
            margin: 0;
        }
        .navbar {
            position: fixed;
            top: 0; left: 0; right: 0;
            height: 64px;
            background: rgba(24,31,42,0.95);
            box-shadow: 0 4px 24px rgba(0,0,0,0.18);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 40px;
            z-index: 100;
            backdrop-filter: var(--glass-blur);
        }
        .logo {
            font-size: 1.3rem;
            font-weight: 700;
            color: var(--accent-1);
            letter-spacing: 2px;
            display: flex;
            align-items: center;
        }
        .logo i { margin-right: 10px; }
        .nav-links { display: flex; gap: 18px; }
        .nav-links a, .nav-links button {
            color: var(--text-main);
            background: linear-gradient(90deg, var(--accent-1), var(--accent-3));
            padding: 10px 28px;
            border-radius: 18px;
            font-weight: 600;
            text-decoration: none;
            font-size: 1rem;
            transition: background 0.2s, transform 0.2s;
            border: none;
            cursor: pointer;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .nav-links a.danger {
            background: linear-gradient(90deg, var(--accent-5), #b71c1c);
        }
        .nav-links a:hover, .nav-links button:hover {
            transform: translateY(-2px) scale(1.05);
            filter: brightness(1.2);
        }
        main {
            margin-top: 90px;
            padding: 0 24px 32px 24px;
            max-width: 1200px;
            margin-left: auto;
            margin-right: auto;
        }
        .card {
            background: var(--card-bg);
            border-radius: 24px;
            box-shadow: 0 4px 24px rgba(0,0,0,0.18);
            padding: 32px 28px 24px 28px;
            display: flex;
            flex-direction: column;
            align-items: flex-start;
            position: relative;
            margin-bottom: 0;
            backdrop-filter: var(--glass-blur);
            min-width: 320px;
            flex: 1 1 350px;
        }
        .card h2 {
            color: var(--accent-1);
            font-size: 1.2rem;
            font-weight: 700;
            margin-bottom: 18px;
        }
        .user-table {
            width: 100%;
            border-collapse: separate;
            border-spacing: 0 8px;
            margin-top: 30px;
            background: rgba(0,0,0,0.08);
            border-radius: 12px;
            overflow: hidden;
        }
        .user-table th {
            padding: 15px;
            text-align: left;
            color: var(--text-sub);
            font-weight: 600;
            border-bottom: 2px solid rgba(0,188,212,0.18);
        }
        .user-table td {
            padding: 15px;
            background: rgba(0,0,0,0.08);
            color: var(--text-main);
        }
        .user-table tr:hover td {
            background: rgba(0,188,212,0.08);
            transform: scale(1.01);
            transition: all 0.3s ease;
        }
        .user-table tr td:first-child {
            border-top-left-radius: 8px;
            border-bottom-left-radius: 8px;
        }
        .user-table tr td:last-child {
            border-top-right-radius: 8px;
            border-bottom-right-radius: 8px;
        }
        .pagination {
            display: flex;
            justify-content: center;
            gap: 10px;
            margin-top: 30px;
        }
        .pagination a {
            padding: 8px 16px;
            background: rgba(0,188,212,0.08);
            color: var(--text-main);
            text-decoration: none;
            border-radius: 8px;
            transition: all 0.3s ease;
        }
        .pagination a:hover {
            background: rgba(0,188,212,0.18);
            transform: translateY(-2px);
        }
        .pagination .current {
            background: rgba(0,188,212,0.25);
            color: var(--text-main);
        }
        .action-buttons {
            display: flex;
            gap: 10px;
        }
        .btn {
            padding: 8px 16px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            display: flex;
            align-items: center;
            gap: 5px;
            transition: all 0.3s ease;
        }
        .btn-primary {
            background: linear-gradient(135deg, var(--accent-1), var(--accent-4));
            color: white;
        }
        .btn-warning {
            background: linear-gradient(135deg, var(--accent-2), var(--accent-6));
            color: white;
        }
        .btn-danger {
            background: linear-gradient(135deg, var(--accent-5), #b71c1c);
            color: white;
        }
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
        }
        .modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            backdrop-filter: blur(5px);
            z-index: 1000;
        }
        .modal-content {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: var(--card-bg);
            padding: 30px;
            border-radius: 15px;
            min-width: 400px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
            color: var(--text-main);
        }
        .modal-header {
            margin-bottom: 20px;
        }
        .modal-header h2 {
            color: var(--accent-1);
            font-size: 20px;
        }
        .form-group {
            margin-bottom: 15px;
        }
        .form-group label {
            display: block;
            margin-bottom: 5px;
            color: var(--text-sub);
        }
        .form-group input {
            width: 100%;
            padding: 10px;
            border: 1.5px solid var(--accent-1);
            border-radius: 8px;
            font-size: 14px;
            background: rgba(30,41,59,0.7);
            color: var(--text-main);
        }
        .form-group input:focus {
            outline: none;
            border-color: var(--accent-2);
            box-shadow: 0 0 5px var(--accent-2);
        }
        .modal-footer {
            display: flex;
            justify-content: flex-end;
            gap: 10px;
            margin-top: 20px;
        }
        .close {
            position: absolute;
            top: 15px;
            right: 15px;
            font-size: 24px;
            color: var(--text-sub);
            cursor: pointer;
        }
        .close:hover {
            color: var(--accent-1);
        }
        .checkbox-group {
            display: flex;
            gap: 20px;
            margin-top: 10px;
        }
        .checkbox-item {
            display: flex;
            align-items: center;
            gap: 5px;
        }
        .toast {
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 15px 25px;
            background: var(--accent-1);
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            display: flex;
            align-items: center;
            gap: 10px;
            z-index: 1000;
            animation: slideIn 0.3s ease;
            color: #fff;
        }
        .toast.success {
            background: var(--accent-4);
            color: white;
        }
        .toast.error {
            background: var(--accent-5);
            color: white;
        }
        @keyframes slideIn {
            from {
                transform: translateX(100%);
                opacity: 0;
            }
            to {
                transform: translateX(0);
                opacity: 1;
            }
        }
        @media (max-width: 900px) {
            main { padding: 0 4px; }
        }
        @media (max-width: 768px) {
            .user-table { font-size: 14px; }
            .user-table th, .user-table td { padding: 10px; }
            .modal-content { min-width: 90vw; }
        }
    </style>
</head>
<body>
    <nav class="navbar">
        <div class="logo"><i class="fas fa-mug-hot"></i> 基于Python的咖啡销售数据分析系统</div>
        <div class="nav-links">
            <a href="/">首页</a>
            <a href="/predict/">返回预测</a>
            <a href="/data/">数据管理</a>
            <a href="/metrics/">模型指标</a>
            <button class="btn btn-primary" onclick="showCreateUserModal()">新建用户</button>
            <a href="/logout/" class="danger">退出登录</a>
        </div>
    </nav>
    <main>
        <div class="card" style="width:100%;">
            <h2>用户管理</h2>
            <table class="user-table">
                <thead>
                    <tr>
                        <th>用户名</th>
                        <th>邮箱</th>
                        <th>最后登录</th>
                        <th>注册时间</th>
                        <th>管理员</th>
                        <th>状态</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody>
                    {% for user in users %}
                    <tr>
                        <td>{{ user.username }}</td>
                        <td>{{ user.email|default:"-" }}</td>
                        <td>{{ user.last_login|date:"Y-m-d H:i:s"|default:"-" }}</td>
                        <td>{{ user.date_joined|date:"Y-m-d H:i:s" }}</td>
                        <td>{{ user.is_superuser|yesno:"是,否" }}</td>
                        <td>{{ user.is_active|yesno:"正常,禁用" }}</td>
                        <td class="action-buttons">
                            <button class="btn btn-warning" onclick="showEditUserModal('{{ user.id }}','{{ user.username }}','{{ user.email|default:'' }}','{{ user.is_superuser|yesno:'true,false' }}','{{ user.is_active|yesno:'true,false' }}')">编辑</button>
                            <button class="btn btn-danger" onclick="deleteUser('{{ user.id }}', '{{ user.username }}')">删除</button>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
            <div class="pagination">
                {% if users.has_previous %}
                    <a href="?page={{ users.previous_page_number }}">上一页</a>
                {% endif %}
                <a class="current">{{ users.number }} / {{ users.paginator.num_pages }}</a>
                {% if users.has_next %}
                    <a href="?page={{ users.next_page_number }}">下一页</a>
                {% endif %}
            </div>
        </div>
    </main>

    <!-- 创建用户模态框 -->
    <div id="createUserModal" class="modal">
        <div class="modal-content">
            <span class="close" onclick="hideModal('createUserModal')">&times;</span>
            <div class="modal-header">
                <h2>创建新用户</h2>
            </div>
            <form id="createUserForm" onsubmit="createUser(event)">
                <div class="form-group">
                    <label>用户名</label>
                    <input type="text" name="username" required>
                </div>
                <div class="form-group">
                    <label>邮箱</label>
                    <input type="email" name="email">
                </div>
                <div class="form-group">
                    <label>密码</label>
                    <input type="password" name="password" required>
                </div>
                <div class="checkbox-group">
                    <div class="checkbox-item">
                        <input type="checkbox" name="is_superuser" id="create_is_superuser">
                        <label for="create_is_superuser">管理员</label>
                    </div>
                    <div class="checkbox-item">
                        <input type="checkbox" name="is_active" id="create_is_active" checked>
                        <label for="create_is_active">启用账号</label>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn" onclick="hideModal('createUserModal')">取消</button>
                    <button type="submit" class="btn btn-primary">创建</button>
                </div>
            </form>
        </div>
    </div>

    <!-- 编辑用户模态框 -->
    <div id="editUserModal" class="modal">
        <div class="modal-content">
            <span class="close" onclick="hideModal('editUserModal')">&times;</span>
            <div class="modal-header">
                <h2>编辑用户</h2>
            </div>
            <form id="editUserForm" onsubmit="updateUser(event)">
                <input type="hidden" name="user_id">
                <div class="form-group">
                    <label>用户名</label>
                    <input type="text" name="username" disabled>
                </div>
                <div class="form-group">
                    <label>邮箱</label>
                    <input type="email" name="email">
                </div>
                <div class="form-group">
                    <label>新密码（留空表示不修改）</label>
                    <input type="password" name="password">
                </div>
                <div class="checkbox-group">
                    <div class="checkbox-item">
                        <input type="checkbox" name="is_superuser" id="edit_is_superuser">
                        <label for="edit_is_superuser">管理员</label>
                    </div>
                    <div class="checkbox-item">
                        <input type="checkbox" name="is_active" id="edit_is_active">
                        <label for="edit_is_active">启用账号</label>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn" onclick="hideModal('editUserModal')">取消</button>
                    <button type="submit" class="btn btn-primary">保存</button>
                </div>
            </form>
        </div>
    </div>

    <script>
        function getCookie(name) {
            let cookieValue = null;
            if (document.cookie && document.cookie !== '') {
                const cookies = document.cookie.split(';');
                for (let i = 0; i < cookies.length; i++) {
                    const cookie = cookies[i].trim();
                    if (cookie.substring(0, name.length + 1) === (name + '=')) {
                        cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
                        break;
                    }
                }
            }
            return cookieValue;
        }

        function showToast(message, type = 'success') {
            const toast = document.createElement('div');
            toast.className = `toast ${type}`;
            toast.innerHTML = `
                <i class="mdi mdi-${type === 'success' ? 'check-circle' : 'alert-circle'}"></i>
                ${message}
            `;
            document.body.appendChild(toast);
            
            setTimeout(() => {
                toast.remove();
            }, 3000);
        }

        function showModal(id) {
            document.getElementById(id).style.display = 'block';
        }

        function hideModal(id) {
            document.getElementById(id).style.display = 'none';
        }

        function showCreateUserModal() {
            document.getElementById('createUserForm').reset();
            showModal('createUserModal');
        }

        function showEditUserModal(userId, username, email, isSuperuser, isActive) {
            const form = document.getElementById('editUserForm');
            form.reset();
            form.user_id.value = userId;
            form.username.value = username;
            form.email.value = email;
            form.is_superuser.checked = (isSuperuser === 'true');
            form.is_active.checked = (isActive === 'true');
            showModal('editUserModal');
        }

        async function createUser(event) {
            event.preventDefault();
            const form = event.target;
            const data = {
                username: form.username.value,
                email: form.email.value,
                password: form.password.value,
                is_superuser: form.is_superuser.checked,
                is_active: form.is_active.checked
            };

            try {
                const response = await fetch('/api/users/create/', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRFToken': getCookie('csrftoken')
                    },
                    body: JSON.stringify(data)
                });

                const result = await response.json();
                if (result.success) {
                    showToast('用户创建成功');
                    hideModal('createUserModal');
                    location.reload();
                } else {
                    showToast(result.error, 'error');
                }
            } catch (error) {
                showToast('创建用户失败', 'error');
            }
        }

        async function updateUser(event) {
            event.preventDefault();
            const form = event.target;
            const userId = form.user_id.value;
            const data = {
                email: form.email.value,
                is_superuser: form.is_superuser.checked,
                is_active: form.is_active.checked
            };

            if (form.password.value) {
                data.password = form.password.value;
            }

            try {
                const response = await fetch(`/api/users/${userId}/update/`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRFToken': getCookie('csrftoken')
                    },
                    body: JSON.stringify(data)
                });

                const result = await response.json();
                if (result.success) {
                    showToast('用户信息更新成功');
                    hideModal('editUserModal');
                    location.reload();
                } else {
                    showToast(result.error, 'error');
                }
            } catch (error) {
                showToast('更新用户信息失败', 'error');
            }
        }

        async function deleteUser(userId, username) {
            if (!confirm(`确定要删除用户 "${username}" 吗？`)) {
                return;
            }

            try {
                const response = await fetch(`/api/users/${userId}/delete/`, {
                    method: 'POST',
                    headers: {
                        'X-CSRFToken': getCookie('csrftoken')
                    }
                });

                const result = await response.json();
                if (result.success) {
                    showToast('用户删除成功');
                    location.reload();
                } else {
                    showToast(result.error, 'error');
                }
            } catch (error) {
                showToast('删除用户失败', 'error');
            }
        }
    </script>
</body>
</html> 