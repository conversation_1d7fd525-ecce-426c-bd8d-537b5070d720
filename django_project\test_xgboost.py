#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
测试XGBoost模型的简单脚本
"""

import os
import sys
import django

# 设置Django环境
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'django_project.settings')
django.setup()

def test_xgboost_import():
    """测试XGBoost导入"""
    try:
        import xgboost as xgb
        print(f"✓ XGBoost导入成功，版本: {xgb.__version__}")
        return True
    except ImportError as e:
        print(f"✗ XGBoost导入失败: {e}")
        print("请运行: pip install xgboost")
        return False

def test_model_training():
    """测试模型训练"""
    try:
        print("\n开始测试XGBoost模型训练...")
        
        # 切换到模型训练目录
        original_cwd = os.getcwd()
        model_dir = 'coffee/模型训练'
        
        if not os.path.exists(model_dir):
            print(f"✗ 模型训练目录不存在: {model_dir}")
            return False
            
        os.chdir(model_dir)
        
        # 检查数据文件
        data_file = 'dataset/coffee_sales_data.csv'
        if not os.path.exists(data_file):
            print(f"✗ 数据文件不存在: {data_file}")
            os.chdir(original_cwd)
            return False
            
        print(f"✓ 数据文件存在: {data_file}")
        
        # 导入并运行模型训练
        sys.path.append('.')
        import model_train
        
        print("开始训练XGBoost模型...")
        r2 = model_train.train_model(data_file)
        print(f"✓ XGBoost模型训练完成，R²值: {r2:.4f}")
        
        # 检查模型文件是否生成
        model_file = 'result/xgboost_model.joblib'
        if os.path.exists(model_file):
            print(f"✓ XGBoost模型文件已生成: {model_file}")
        else:
            print(f"✗ XGBoost模型文件未生成: {model_file}")
            
        # 恢复工作目录
        os.chdir(original_cwd)
        
        return r2 > 0.3
        
    except Exception as e:
        print(f"✗ XGBoost模型训练失败: {e}")
        import traceback
        traceback.print_exc()
        os.chdir(original_cwd)
        return False

def test_model_loading():
    """测试模型加载"""
    try:
        print("\n开始测试XGBoost模型加载...")
        
        model_path = 'coffee/模型训练/result/xgboost_model.joblib'
        if not os.path.exists(model_path):
            print(f"✗ XGBoost模型文件不存在: {model_path}")
            return False
            
        import joblib
        model, pred_std, label_encoders = joblib.load(model_path)
        
        print(f"✓ XGBoost模型加载成功")
        print(f"  模型类型: {type(model).__name__}")
        print(f"  决策树数量: {model.n_estimators}")
        print(f"  最大深度: {model.max_depth}")
        print(f"  学习率: {model.learning_rate}")
        print(f"  预测标准差: {pred_std}")
        print(f"  标签编码器: {list(label_encoders.keys())}")
        
        return True
        
    except Exception as e:
        print(f"✗ XGBoost模型加载失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("=== XGBoost模型测试 ===")
    
    # 1. 测试XGBoost导入
    if not test_xgboost_import():
        return
    
    # 2. 测试模型训练
    if not test_model_training():
        print("\n✗ 模型训练测试失败")
        return
    
    # 3. 测试模型加载
    if not test_model_loading():
        print("\n✗ 模型加载测试失败")
        return
    
    print("\n✓ 所有XGBoost测试通过！")
    print("现在可以使用XGBoost模型进行预测了。")

if __name__ == '__main__':
    main()
