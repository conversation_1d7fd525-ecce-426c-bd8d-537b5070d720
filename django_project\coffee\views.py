from django.shortcuts import render, redirect, get_object_or_404
from django.http import JsonResponse
from django.contrib.auth import authenticate, login, logout
from django.contrib.auth.models import User
from django.contrib import messages
from django.core.paginator import Paginator
from .models import CoffeeOrder
from django.db.models import Avg, Count, Sum, F, FloatField, StdDev
from django.db.models.functions import ExtractHour
import json
from django.views.decorators.csrf import csrf_exempt
from django.contrib.auth.decorators import login_required, user_passes_test
from django.core.cache import cache
from datetime import datetime, timedelta
import threading
import time
import joblib
from django.core.mail import send_mail
from apscheduler.schedulers.background import BackgroundScheduler
from django.conf import settings
import os
import numpy as np
import pandas as pd
from sklearn import preprocessing


def login_view(request):
    if request.method == 'POST':
        username = request.POST.get('username')
        password = request.POST.get('password')
        user = authenticate(request, username=username, password=password)
        if user is not None:
            login(request, user)
            messages.success(request, '登录成功！')
            return redirect('index')
        else:
            messages.error(request, '用户名或密码错误！')
    return render(request, 'login.html')


def register_view(request):
    if request.method == 'POST':
        username = request.POST.get('username')
        password = request.POST.get('password')
        confirm_password = request.POST.get('confirm_password')

        if password != confirm_password:
            messages.error(request, '两次输入的密码不一致！')
            return render(request, 'register.html')

        if User.objects.filter(username=username).exists():
            messages.error(request, '用户名已存在！')
            return render(request, 'register.html')

        user = User.objects.create_user(username=username, password=password)
        messages.success(request, '注册成功！请登录。')
        return redirect('login')
    return render(request, 'register.html')


def logout_view(request):
    logout(request)
    messages.success(request, '已成功退出登录！')
    return redirect('login')


def index(request):
    return render(request, 'index.html')


def basic_stats(request):
    """基本统计信息"""
    total_orders = CoffeeOrder.objects.count()
    stats = CoffeeOrder.objects.aggregate(
        total_sales=Sum('paid_amount'),
        avg_order_value=Avg('paid_amount'),
        total_items=Sum('quantity')
    )
    
    return JsonResponse({
        'success': True,
        'data': {
            'total_orders': total_orders,
            'total_sales': float(stats['total_sales'] or 0),
            'avg_order_value': float(stats['avg_order_value'] or 0),
            'total_items': int(stats['total_items'] or 0)
        }
    })


def sales_trend(request):
    """销售趋势分析"""
    daily_sales = CoffeeOrder.objects.values('order_date').annotate(
        sales=Sum('paid_amount'),
        orders=Count('order_id')
    ).order_by('order_date')
    
    return JsonResponse({
        'success': True,
        'data': {
            'dates': [item['order_date'].strftime('%Y-%m-%d') for item in daily_sales],
            'sales': [float(item['sales']) for item in daily_sales],
            'orders': [item['orders'] for item in daily_sales]
        }
    })


def update_product_ranking():
    """更新产品销售排行榜数据"""
    while True:
        product_stats = CoffeeOrder.objects.values('product_name').annotate(
            total_quantity=Sum('quantity'),
            total_sales=Sum('paid_amount')
        ).order_by('-total_sales')[:10]
        
        cache.set('product_ranking', list(product_stats), 300)  # 缓存5分钟
        time.sleep(300)  # 每5分钟更新一次

# 启动后台更新线程
update_thread = threading.Thread(target=update_product_ranking, daemon=True)
update_thread.start()

def product_analysis(request):
    """产品销售TOP10分析"""
    force_update = request.GET.get('force_update', '0') == '1'
    
    if force_update:
        # 强制更新数据
        product_stats = CoffeeOrder.objects.values('product_name').annotate(
            total_quantity=Sum('quantity'),
            total_sales=Sum('paid_amount')
        ).order_by('-total_sales')[:10]
        cache.set('product_ranking', list(product_stats), 300)
    else:
        # 获取缓存数据
        product_stats = cache.get('product_ranking')
        if not product_stats:
            product_stats = CoffeeOrder.objects.values('product_name').annotate(
                total_quantity=Sum('quantity'),
                total_sales=Sum('paid_amount')
            ).order_by('-total_sales')[:10]
            cache.set('product_ranking', list(product_stats), 300)
    
    return JsonResponse({
        'success': True,
        'data': {
            'products': [item['product_name'] for item in product_stats],
            'quantities': [item['total_quantity'] for item in product_stats],
            'sales': [float(item['total_sales']) for item in product_stats],
            'update_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        }
    })


def payment_analysis(request):
    """支付方式分析"""
    payment_stats = CoffeeOrder.objects.values('payment_method').annotate(
        count=Count('order_id'),
        total_amount=Sum('paid_amount')
    ).order_by('-count')
    
    return JsonResponse({
        'success': True,
        'data': [{
            'name': item['payment_method'],
            'orders': item['count'],
            'amount': float(item['total_amount'])
        } for item in payment_stats]
    })


def member_analysis(request):
    """会员等级分析"""
    member_stats = CoffeeOrder.objects.values('member_level').annotate(
        orders=Count('order_id'),
        total_sales=Sum('paid_amount'),
        avg_discount=Avg('discount_rate')
    ).order_by('-total_sales')
    
    return JsonResponse({
        'success': True,
        'data': [{
            'level': item['member_level'],
            'orders': item['orders'],
            'sales': float(item['total_sales']),
            'avg_discount': float(item['avg_discount'])
        } for item in member_stats]
    })


def hourly_sales(request):
    """小时销售分布"""
    hourly_stats = CoffeeOrder.objects.annotate(
        hour=ExtractHour('order_time')
    ).values('hour').annotate(
        orders=Count('order_id'),
        sales=Sum('paid_amount')
    ).order_by('hour')
    
    return JsonResponse({
        'success': True,
        'data': {
            'hours': [item['hour'] for item in hourly_stats],
            'orders': [item['orders'] for item in hourly_stats],
            'sales': [float(item['sales']) for item in hourly_stats]
        }
    })


def discount_analysis(request):
    """折扣效果分析"""
    discount_stats = CoffeeOrder.objects.values('discount_rate').annotate(
        order_count=Count('order_id'),
        total_original=Sum('original_price'),
        total_paid=Sum('paid_amount')
    ).order_by('discount_rate')
    
    return JsonResponse({
        'success': True,
        'data': [{
            'discount_rate': float(item['discount_rate']),
            'order_count': int(item['order_count']),
            'total_original': float(item['total_original']),
            'total_paid': float(item['total_paid']),
            'savings': float(item['total_original'] - item['total_paid'])
        } for item in discount_stats]
    })


# 全局变量
MODEL_PATH = 'coffee/模型训练/result/random_forest_model.joblib'
METRICS_PATH = 'coffee/模型训练/result/model_evaluation.txt'

def predict_page(request):
    """预测页面"""
    return render(request, 'coffee_predict.html')

def test_predict_page(request):
    """测试预测页面"""
    return render(request, 'test_predict.html')

def predict_sales(request):      # 这里是算法以及预测分析模块
    """销售预测API"""
    try:
        print('开始处理预测请求')
        data = json.loads(request.body)
        print('请求数据:', data)
        start_date = datetime.strptime(data['date'], '%Y-%m-%d')
        print('起始日期:', start_date)

        # 检查模型文件是否存在
        model_path = 'coffee/模型训练/result/random_forest_model.joblib'
        if not os.path.exists(model_path):
            print('随机森林模型文件不存在:', model_path)
            return JsonResponse({
                'success': False,
                'error': '随机森林模型文件不存在，请先训练模型'
            })

        # 加载随机森林模型、预测标准差和标签编码器
        print('加载随机森林模型文件...')
        model, pred_std, label_encoders = joblib.load(model_path)
        print('随机森林模型加载成功')
        
        # 创建预测日期范围
        future_dates = pd.date_range(start=start_date, periods=7, freq='D')
        
        # 获取最近30天的数据用于计算平均特征值
        end_date = datetime.now()
        start_date_30d = end_date - timedelta(days=2000)
        
        recent_data = CoffeeOrder.objects.filter(
            order_date__range=[start_date_30d, end_date]
        ).values()
        
        print('获取到的历史数据条数:', len(recent_data))
        

        if not recent_data:
            print('没有历史数据，使用默认数据')
            default_data = [{
                'order_date': end_date.date(),
                'order_time': '12:00:00',
                'payment_method': '微信支付',
                'member_level': '普通会员',
                'product_name': '美式咖啡',
                'quantity': 2,
                'discount_rate': 1.0
            }]
            recent_data = default_data

        df = pd.DataFrame(list(recent_data))
        print('处理后的数据框:', df.shape)
        print('数据框列名:', df.columns.tolist())

        # 数据库字段已经是正确的名称，直接使用
        df['order_date'] = pd.to_datetime(df['order_date'])

        # 处理时间列 - 从order_time提取小时
        if 'order_time' in df.columns:
            # order_time是time对象，需要转换为小时
            df['hour'] = df['order_time'].apply(lambda x: x.hour if hasattr(x, 'hour') else
                                               datetime.strptime(str(x), '%H:%M:%S').hour)
        else:
            print('缺少order_time列，使用默认小时')
            df['hour'] = 12  # 默认中午12点
        
        # 处理新的标签值
        feature_mapping = {
            '支付方式': 'payment_method',
            '会员等级': 'member_level',
            '产品名称': 'product_name'
        }

        print('开始处理分类特征编码')
        for feature, column in feature_mapping.items():
            if column not in df.columns:
                print(f'缺少列 {column}，使用默认值')
                # 设置默认值
                if column == 'payment_method':
                    df[column] = '微信支付'
                elif column == 'member_level':
                    df[column] = '普通会员'
                elif column == 'product_name':
                    df[column] = '美式咖啡'

            # 获取当前特征的所有唯一值
            unique_values = df[column].unique()
            print(f'{feature} 唯一值:', unique_values)

            # 检查是否有新的标签值
            if feature in label_encoders:
                new_labels = set(unique_values) - set(label_encoders[feature].classes_)
                if new_labels:
                    print(f'{feature} 发现新标签:', new_labels)
                    # 如果有新的标签值，重新训练编码器
                    all_labels = list(label_encoders[feature].classes_) + list(new_labels)
                    new_encoder = preprocessing.LabelEncoder()
                    new_encoder.fit(all_labels)
                    # 更新编码器
                    label_encoders[feature] = new_encoder
            else:
                print(f'{feature} 编码器不存在，创建新的')
                label_encoders[feature] = preprocessing.LabelEncoder()
                label_encoders[feature].fit(unique_values)
                
        # 计算平均特征值
        print('计算平均特征值')

        # 确保数量和折扣率列存在
        if 'quantity' not in df.columns:
            df['quantity'] = 2  # 默认数量
        if 'discount_rate' not in df.columns:
            df['discount_rate'] = 1.0  # 默认无折扣

        avg_features = {
            'hour': df['hour'].mean(),
            '支付方式_encoded': label_encoders['支付方式'].transform(df['payment_method']).mean(),
            '会员等级_encoded': label_encoders['会员等级'].transform(df['member_level']).mean(),
            '产品名称_encoded': label_encoders['产品名称'].transform(df['product_name']).mean(),
            '数量': df['quantity'].mean(),
            '折扣率': df['discount_rate'].mean()
        }
        
        print('特征值:', avg_features)
        
        # 创建未来数据的特征
        future_df = pd.DataFrame({'order_date': future_dates})
        future_df['hour'] = avg_features['hour']
        future_df['dayofweek'] = future_df['order_date'].dt.dayofweek
        future_df['quarter'] = future_df['order_date'].dt.quarter
        future_df['month'] = future_df['order_date'].dt.month
        future_df['year'] = future_df['order_date'].dt.year
        future_df['支付方式_encoded'] = avg_features['支付方式_encoded']
        future_df['会员等级_encoded'] = avg_features['会员等级_encoded']
        future_df['产品名称_encoded'] = avg_features['产品名称_encoded']
        future_df['数量'] = avg_features['数量']
        future_df['折扣率'] = avg_features['折扣率']
        
        print('预测数据框:', future_df.shape)
        
        # 准备特征数据
        feature_columns = ['hour', 'dayofweek', 'quarter', 'month', 'year',
                         '支付方式_encoded', '会员等级_encoded', '产品名称_encoded',
                         '数量', '折扣率']
        X = future_df[feature_columns]
        
        # 使用随机森林模型进行预测
        print('开始随机森林预测')
        predictions = model.predict(X)
        print('随机森林预测完成')

        # 计算预测区间（随机森林使用预测标准差）
        lower_bound = predictions - 1.96 * pred_std
        upper_bound = predictions + 1.96 * pred_std
        
        # 准备预测结果
        result_predictions = []
        for i, date in enumerate(future_dates):
            result_predictions.append({
                'date': date.strftime('%Y-%m-%d'),
                'amount': round(float(predictions[i]), 2),
                'lower_bound': round(float(lower_bound[i]), 2),
                'upper_bound': round(float(upper_bound[i]), 2)
            })
        
        print('预测结果:', len(result_predictions))
        
        # 保存更新后的随机森林模型和编码器
        joblib.dump((model, pred_std, label_encoders), 'coffee/模型训练/result/random_forest_model.joblib')
        
        return JsonResponse({
            'success': True,
            'data': {
                'predictions': result_predictions
            }
        })
    except Exception as e:
        print('预测出错:', str(e))
        return JsonResponse({
            'success': False,
            'error': str(e)
        })

def model_metrics(request):
    """获取模型评估指标"""
    try:
        with open('coffee/模型训练/result/model_evaluation.txt', 'r', encoding='utf-8') as f:
            metrics = f.read()
        return JsonResponse({
            'success': True,
            'data': {
                'metrics': metrics
            }
        })
    except Exception as e:
        return JsonResponse({
            'success': False,
            'error': str(e)
        })

def retrain_model():
    """重新训练模型"""
    try:
        # 导入训练模块
        import sys
        sys.path.append('coffee/模型训练')
        import model_train
        
        # 获取最近30天的数据
        end_date = datetime.now()
        start_date = end_date - timedelta(days=30)
        
        recent_data = CoffeeOrder.objects.filter(
            order_date__range=[start_date, end_date]
        ).values()
        
        # 转换为DataFrame
        df = pd.DataFrame(list(recent_data))
        
        # 保存为临时CSV文件
        temp_csv_path = 'coffee/模型训练/dataset/temp_coffee_sales_data.csv'
        df.to_csv(temp_csv_path, index=False)
        
        # 重新训练模型
        r2 = model_train.train_model(temp_csv_path)
        
        # 检查模型质量
        if r2 < 0.6:
            # 发送邮件通知管理员
            send_mail(
                '模型质量警告',
                f'模型R²值为{r2:.4f},低于阈值0.6,请检查数据质量。',
                settings.EMAIL_HOST_USER,
                [admin.email for admin in User.objects.filter(is_superuser=True)],
                fail_silently=False,
            )
        
        # 删除临时文件
        os.remove(temp_csv_path)
        
    except Exception as e:
        print(f"模型重训练失败: {str(e)}")

# 初始化定时任务
scheduler = BackgroundScheduler()
scheduler.add_job(retrain_model, 'cron', day_of_week='sun', hour=0, minute=0)
scheduler.start()

def user_management(request):
    """用户管理页面"""
    users = User.objects.all().order_by('-date_joined')
    paginator = Paginator(users, 10)  # 每页显示10条
    page = request.GET.get('page', 1)
    users_page = paginator.get_page(page)
    
    return render(request, 'user_management.html', {
        'users': users_page
    })

def data_management(request):
    """数据管理页面"""
    orders = CoffeeOrder.objects.all().order_by('-order_date', '-order_time')
    
    paginator = Paginator(orders, 20)  # 每页显示20条
    page = request.GET.get('page', 1)
    orders_page = paginator.get_page(page)
    
    return render(request, 'data_management.html', {
        'orders': orders_page
    })

def metrics_page(request):
    """模型指标页面"""
    try:
        # 读取模型评估指标
        with open('coffee/模型训练/result/model_evaluation.txt', 'r', encoding='utf-8') as f:
            metrics = f.read()
            
        # 加载随机森林模型和特征重要性
        model, pred_std, label_encoders = joblib.load('coffee/模型训练/result/random_forest_model.joblib')

        # 获取随机森林特征重要性
        feature_importance = pd.DataFrame({
            'feature': ['hour', 'dayofweek', 'quarter', 'month', 'year',
                       '支付方式', '会员等级', '产品名称', '数量', '折扣率'],
            'importance': model.feature_importances_
        })
        feature_importance = feature_importance.sort_values('importance', ascending=False)

        # 获取随机森林模型文件的最后修改时间并格式化
        model_file = 'coffee/模型训练/result/random_forest_model.joblib'
        last_modified_timestamp = os.path.getmtime(model_file)
        last_modified_time = datetime.fromtimestamp(last_modified_timestamp).strftime('%Y-%m-%d %H:%M:%S')
        
        return render(request, 'model_metrics.html', {
            'metrics': metrics,
            'feature_importance': feature_importance.to_dict('records'),
            'last_retrain_time': last_modified_time
        })
    except Exception as e:
        messages.error(request, f'获取模型指标失败: {str(e)}')
        return redirect('index')

@user_passes_test(lambda u: u.is_superuser)
def create_user(request):
    """创建新用户"""
    if request.method == 'POST':
        try:
            data = json.loads(request.body)
            username = data.get('username')
            email = data.get('email')
            password = data.get('password')
            is_superuser = data.get('is_superuser', False)
            is_active = data.get('is_active', True)
            
            if User.objects.filter(username=username).exists():
                return JsonResponse({'success': False, 'error': '用户名已存在'})
            
            user = User.objects.create_user(
                username=username,
                email=email,
                password=password,
                is_superuser=is_superuser,
                is_active=is_active
            )
            
            return JsonResponse({
                'success': True,
                'data': {
                    'id': user.id,
                    'username': user.username,
                    'email': user.email,
                    'is_superuser': user.is_superuser,
                    'is_active': user.is_active,
                    'date_joined': user.date_joined.strftime('%Y-%m-%d %H:%M:%S')
                }
            })
        except Exception as e:
            return JsonResponse({'success': False, 'error': str(e)})
    return JsonResponse({'success': False, 'error': '不支持的请求方法'})

@user_passes_test(lambda u: u.is_superuser)
def update_user(request, user_id):
    """更新用户信息"""
    if request.method == 'POST':
        try:
            user = get_object_or_404(User, id=user_id)
            data = json.loads(request.body)
            
            if 'email' in data:
                user.email = data['email']
            if 'is_active' in data:
                user.is_active = data['is_active']
            if 'is_superuser' in data:
                user.is_superuser = data['is_superuser']
            if 'password' in data:
                user.set_password(data['password'])
                
            user.save()
            
            return JsonResponse({
                'success': True,
                'data': {
                    'id': user.id,
                    'username': user.username,
                    'email': user.email,
                    'is_superuser': user.is_superuser,
                    'is_active': user.is_active
                }
            })
        except Exception as e:
            return JsonResponse({'success': False, 'error': str(e)})
    return JsonResponse({'success': False, 'error': '不支持的请求方法'})

@user_passes_test(lambda u: u.is_superuser)
def delete_user(request, user_id):
    """删除用户"""
    if request.method == 'POST':
        try:
            user = get_object_or_404(User, id=user_id)
            if user.id == request.user.id:
                return JsonResponse({'success': False, 'error': '不能删除当前登录用户'})
            user.delete()
            return JsonResponse({'success': True})
        except Exception as e:
            return JsonResponse({'success': False, 'error': str(e)})
    return JsonResponse({'success': False, 'error': '不支持的请求方法'})

@user_passes_test(lambda u: u.is_superuser)
def create_order(request):
    """创建新订单"""
    if request.method == 'POST':
        try:
            data = json.loads(request.body)
            order = CoffeeOrder.objects.create(
                order_id=data['order_id'],
                order_date=data['order_date'],
                order_time=data['order_time'],
                product_name=data['product_name'],
                quantity=data['quantity'],
                original_price=data['original_price'],
                paid_amount=data['paid_amount'],
                payment_method=data['payment_method'],
                member_level=data['member_level'],
                discount_rate=data['discount_rate']
            )
            
            return JsonResponse({
                'success': True,
                'data': {
                    'id': order.id,
                    'order_id': order.order_id,
                    'order_date': order.order_date.strftime('%Y-%m-%d'),
                    'order_time': order.order_time.strftime('%H:%M:%S')
                }
            })
        except Exception as e:
            return JsonResponse({'success': False, 'error': str(e)})
    return JsonResponse({'success': False, 'error': '不支持的请求方法'})

@user_passes_test(lambda u: u.is_superuser)
def update_order(request, order_id):
    """更新订单信息"""
    if request.method == 'POST':
        try:
            order = get_object_or_404(CoffeeOrder, order_id=order_id)
            data = json.loads(request.body)
            
            # 特殊处理时间字段
            if 'order_time' in data:
                try:
                    # 尝试解析时间字符串
                    time_str = data['order_time'].strip()
                    if not time_str:
                        return JsonResponse({
                            'success': False,
                            'error': '时间不能为空'
                        })
                    
                    # 处理不同的时间格式
                    try:
                        if ':' in time_str:
                            # 处理 HH:MM 或 HH:MM:SS 格式
                            parts = time_str.split(':')
                            if len(parts) == 2:
                                time_str = f"{time_str}:00"

                            print(time_str)
                            parsed_time = datetime.strptime(time_str, '%H:%M:%S').time()
                            print('-----------')
                            print(parsed_time)
                        else:
                            # 如果是其他格式，尝试转换
                            parsed_time = datetime.strptime(time_str, '%H%M%S').time()
                            print(parsed_time)
                        data['order_time'] = parsed_time
                        print('00000000000000')
                    except ValueError:
                        # 如果转换失败，返回错误信息
                        return JsonResponse({
                            'success': False,
                            'error': '时间格式无效，请使用 HH:MM:SS 或 HH:MM 格式'
                        })
                except Exception as e:
                    return JsonResponse({
                        'success': False,
                        'error': f'时间处理错误: {str(e)}'
                    })
            
            # 更新其他字段
            for field, value in data.items():
                if hasattr(order, field):
                    setattr(order, field, value)
            
            order.save()
            
            # 确保返回的时间是 time 对象
            return JsonResponse({
                'success': True,
                'data': {
                    'order_id': order.order_id,
                    'order_date': order.order_date,
                    'order_time': order.order_time
                }
            })
        except Exception as e:
            return JsonResponse({'success': False, 'error': str(e)})
    return JsonResponse({'success': False, 'error': '不支持的请求方法'})

@user_passes_test(lambda u: u.is_superuser)
def delete_order(request, order_id):
    """删除订单"""
    if request.method == 'POST':
        try:
            order = get_object_or_404(CoffeeOrder, order_id=order_id)
            order.delete()
            return JsonResponse({'success': True})
        except Exception as e:
            return JsonResponse({'success': False, 'error': str(e)})
    return JsonResponse({'success': False, 'error': '不支持的请求方法'})
