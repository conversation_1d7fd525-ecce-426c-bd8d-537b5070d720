<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>淘宝购买行为预测</title>
    <script src="https://cdn.jsdelivr.net/npm/echarts@5.4.3/dist/echarts.min.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            background: linear-gradient(135deg, #1a1c23 0%, #242830 100%);
            color: #e1e1e1;
            font-family: "Microsoft YaHei", Arial, sans-serif;
            min-height: 100vh;
            padding: 30px;
            position: relative;
            overflow-x: hidden;
        }

        body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: 
                radial-gradient(circle at 20% 20%, rgba(255, 87, 34, 0.15) 0%, transparent 30%),
                radial-gradient(circle at 80% 80%, rgba(255, 193, 7, 0.15) 0%, transparent 30%),
                radial-gradient(circle at 50% 50%, rgba(233, 30, 99, 0.1) 0%, transparent 50%);
            pointer-events: none;
            z-index: -1;
            animation: gradientMove 15s ease infinite;
        }

        @keyframes gradientMove {
            0% { transform: scale(1); }
            50% { transform: scale(1.1); }
            100% { transform: scale(1); }
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            position: relative;
        }

        .header {
            text-align: center;
            margin-bottom: 40px;
            padding: 30px;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 20px;
            position: relative;
            overflow: hidden;
            border: 1px solid rgba(255, 255, 255, 0.1);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .header h1 {
            color: #fff;
            font-size: 32px;
            margin-bottom: 15px;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
            position: relative;
        }

        .nav-links {
            display: flex;
            gap: 15px;
            position: relative;
            z-index: 1;
        }

        .nav-links a {
            padding: 12px 24px;
            color: white;
            text-decoration: none;
            border-radius: 12px;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            overflow: hidden;
            display: flex;
            align-items: center;
            justify-content: center;
            letter-spacing: 0.5px;
        }
        
        .nav-links a::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, rgba(255, 87, 34, 0.8), rgba(255, 193, 7, 0.8));
            border-radius: 12px;
            transform: translateY(100%);
            transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            z-index: -1;
        }
        
        .nav-links a:hover::before {
            transform: translateY(0);
        }
        
        .nav-links a.primary {
            background: linear-gradient(135deg, #ff5722, #ffc107);
            box-shadow: 0 4px 15px rgba(255, 87, 34, 0.2);
        }
        
        .nav-links a.danger {
            background: linear-gradient(135deg, #f44336, #ff5252);
            box-shadow: 0 4px 15px rgba(244, 67, 54, 0.2);
        }
        
        .nav-links a.admin {
            background: linear-gradient(135deg, #ff9800, #ff5722);
            box-shadow: 0 4px 15px rgba(255, 152, 0, 0.2);
        }

        .main-content {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 40px;
            position: relative;
        }

        .input-section, .result-section {
            background: rgba(255, 255, 255, 0.05);
            padding: 35px;
            border-radius: 20px;
            border: 1px solid rgba(255, 255, 255, 0.1);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            overflow: hidden;
        }

        .input-section h2, .result-section h2 {
            color: #fff;
            font-size: 24px;
            margin-bottom: 25px;
            padding-bottom: 15px;
            border-bottom: 2px solid rgba(255, 255, 255, 0.1);
            position: relative;
        }

        .input-group {
            margin-bottom: 30px;
            background: rgba(0, 0, 0, 0.2);
            padding: 20px;
            border-radius: 15px;
            border: 1px solid rgba(255, 255, 255, 0.05);
        }

        .input-field {
            margin-bottom: 20px;
        }

        .input-field label {
            display: block;
            margin-bottom: 8px;
            color: #fff;
            font-size: 14px;
            font-weight: 500;
            letter-spacing: 0.5px;
        }

        .input-field input[type="number"] {
            width: 100%;
            padding: 12px 15px;
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 12px;
            background: rgba(255, 255, 255, 0.05);
            color: #fff;
            font-size: 14px;
            transition: all 0.3s ease;
        }

        .input-field input:focus {
            outline: none;
            border-color: rgba(255, 87, 34, 0.7);
            box-shadow: 0 0 0 4px rgba(255, 87, 34, 0.2);
        }

        .submit-btn {
            width: 100%;
            padding: 16px;
            background: linear-gradient(135deg, #ff5722, #ffc107);
            border: none;
            border-radius: 14px;
            color: #fff;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
            position: relative;
            overflow: hidden;
            letter-spacing: 1px;
            box-shadow: 0 4px 15px rgba(255, 87, 34, 0.3);
        }

        .submit-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(255, 87, 34, 0.4);
        }

        .result-card {
            background: rgba(255, 255, 255, 0.08);
            padding: 25px;
            border-radius: 15px;
            margin-bottom: 25px;
            border: 1px solid rgba(255, 255, 255, 0.1);
            transition: all 0.3s ease;
        }

        .result-card h3 {
            color: #fff;
            margin-bottom: 20px;
            font-size: 18px;
            font-weight: 600;
            letter-spacing: 0.5px;
        }

        .purchase-probability {
            font-size: 48px;
            font-weight: bold;
            margin: 25px 0;
            text-align: center;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
            transition: all 0.3s ease;
        }

        .purchase-probability.high {
            background: linear-gradient(135deg, #ff5722, #ffc107);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            animation: pulseOrange 2s ease infinite;
        }

        .purchase-probability.medium {
            background: linear-gradient(135deg, #ffc107, #ff9800);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            animation: pulseYellow 2s ease infinite;
        }

        .purchase-probability.low {
            background: linear-gradient(135deg, #ff9800, #ff5722);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            animation: pulseRed 2s ease infinite;
        }

        #featureImportance {
            height: 300px;
            border-radius: 10px;
            overflow: hidden;
        }

        .loading-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.7);
            display: none;
            justify-content: center;
            align-items: center;
            z-index: 1000;
            backdrop-filter: blur(5px);
        }

        .loading-content {
            background: rgba(255, 255, 255, 0.1);
            padding: 30px 50px;
            border-radius: 15px;
            text-align: center;
            border: 1px solid rgba(255, 255, 255, 0.1);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }

        .loading-spinner {
            width: 50px;
            height: 50px;
            border: 5px solid rgba(255, 255, 255, 0.1);
            border-top: 5px solid #ff5722;
            border-radius: 50%;
            margin: 0 auto 20px;
            animation: spin 1s linear infinite;
        }

        .loading-text {
            color: #fff;
            font-size: 18px;
            margin-bottom: 10px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        @media (max-width: 768px) {
            .main-content {
                grid-template-columns: 1fr;
                gap: 30px;
            }
            
            .container {
                padding: 10px;
            }

            .header {
                padding: 20px;
            }

            .header h1 {
                font-size: 24px;
            }
        }
    </style>
</head>
<body>
    <div class="loading-overlay">
        <div class="loading-content">
            <div class="loading-spinner"></div>
            <div class="loading-text">正在进行预测分析</div>
        </div>
    </div>

    <div class="container">
        <div class="header">
            <h1>淘宝购买行为预测</h1>
            <div class="nav-links">
                {% if user.is_authenticated %}
                    {% if user.is_superuser %}
                        <a href="/user_management/" class="admin">用户管理</a>
                        <a href="/data_management/" class="admin">数据管理</a>
                    {% endif %}
                    <a href="/" class="primary">返回首页</a>
                    <a href="/logout/" class="danger">退出登录</a>
                {% else %}
                    <a href="/login/" class="primary">登录</a>
                    <a href="/register/" class="primary">注册</a>
                {% endif %}
            </div>
        </div>

        <div class="main-content">
            <div class="input-section">
                <h2>输入会话信息</h2>
                <form id="predictionForm">
                    <div class="input-group">
                        <div class="input-field">
                            <label>会话时长（秒）</label>
                            <input type="number" name="duration" required min="0" step="0.01">
                        </div>
                        <div class="input-field">
                            <label>点击商品数量</label>
                            <input type="number" name="cCount" required min="0">
                        </div>
                        <div class="input-field">
                            <label>加购商品数量</label>
                            <input type="number" name="bCount" required min="0">
                        </div>
                    </div>

                    <div class="input-group">
                        <div class="input-field">
                            <label>用户评分</label>
                            <input type="number" name="customerScore" required min="0">
                        </div>
                        <div class="input-field">
                            <label>用户年龄</label>
                            <input type="number" name="age" required min="1" max="100">
                        </div>
                    </div>

                    <button type="submit" class="submit-btn">开始预测</button>
                </form>
            </div>

            <div class="result-section">
                <h2>预测结果</h2>
                <div class="result-card">
                    <h3>购买概率</h3>
                    <div class="purchase-probability" id="purchaseProbability">-</div>
                </div>
                <div class="result-card">
                    <h3>主要影响因素</h3>
                    <div id="featureImportance"></div>
                </div>
            </div>
        </div>
    </div>

    <script>
        const predictionForm = document.getElementById('predictionForm');
        const featureChart = echarts.init(document.getElementById('featureImportance'));
        const purchaseProbability = document.getElementById('purchaseProbability');
        const loadingOverlay = document.querySelector('.loading-overlay');
        
        function showLoading() {
            loadingOverlay.style.display = 'flex';
        }
        
        function hideLoading() {
            loadingOverlay.style.display = 'none';
        }
        
        predictionForm.addEventListener('submit', async (e) => {
            e.preventDefault();
            showLoading();
            
            const formData = new FormData(predictionForm);
            const data = {};
            
            for (let [key, value] of formData.entries()) {
                data[key] = value;
            }
            
            try {
                const response = await fetch('/api/predict_purchase/', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRFToken': getCookie('csrftoken')
                    },
                    body: JSON.stringify(data)
                });
                
                const result = await response.json();
                
                if (result.success) {
                    const probability = result.data.purchase_probability;
                    purchaseProbability.textContent = `${probability}%`;
                    
                    purchaseProbability.classList.remove('high', 'medium', 'low');
                    
                    if (probability > 70) {
                        purchaseProbability.classList.add('high');
                    } else if (probability > 30) {
                        purchaseProbability.classList.add('medium');
                    } else {
                        purchaseProbability.classList.add('low');
                    }
                    
                    const option = {
                        tooltip: {
                            trigger: 'axis',
                            axisPointer: {
                                type: 'shadow'
                            }
                        },
                        grid: {
                            left: '3%',
                            right: '4%',
                            bottom: '3%',
                            containLabel: true
                        },
                        xAxis: {
                            type: 'value',
                            axisLabel: {
                                color: '#fff'
                            },
                            splitLine: {
                                lineStyle: {
                                    color: 'rgba(255, 255, 255, 0.1)'
                                }
                            }
                        },
                        yAxis: {
                            type: 'category',
                            data: result.data.top_features.map(f => f.name),
                            axisLabel: {
                                color: '#fff'
                            }
                        },
                        series: [{
                            name: '重要性',
                            type: 'bar',
                            data: result.data.top_features.map(f => ({
                                value: f.importance,
                                itemStyle: {
                                    color: new echarts.graphic.LinearGradient(0, 0, 1, 0, [{
                                        offset: 0,
                                        color: '#ff5722'
                                    }, {
                                        offset: 1,
                                        color: '#ffc107'
                                    }])
                                }
                            }))
                        }]
                    };
                    featureChart.setOption(option);
                } else {
                    alert('预测失败：' + result.error);
                }
            } catch (error) {
                alert('请求失败：' + error.message);
            } finally {
                hideLoading();
            }
        });

        function getCookie(name) {
            let cookieValue = null;
            if (document.cookie && document.cookie !== '') {
                const cookies = document.cookie.split(';');
                for (let i = 0; i < cookies.length; i++) {
                    const cookie = cookies[i].trim();
                    if (cookie.substring(0, name.length + 1) === (name + '=')) {
                        cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
                        break;
                    }
                }
            }
            return cookieValue;
        }

        window.addEventListener('resize', function() {
            featureChart.resize();
        });
    </script>
</body>
</html> 