<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>数据管理 - 咖啡销售预测系统</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
    <link href="https://fonts.googleapis.com/css?family=Inter:400,600,700&display=swap" rel="stylesheet">
    <style>
        :root {
            --primary-bg: #181f2a;
            --card-bg: rgba(30, 41, 59, 0.85);
            --accent-1: #00bcd4;
            --accent-2: #ff9800;
            --accent-3: #8e24aa;
            --accent-4: #43a047;
            --accent-5: #f44336;
            --accent-6: #ffd600;
            --text-main: #fff;
            --text-sub: #b0bec5;
            --glass-blur: blur(12px);
        }
        body {
            background: linear-gradient(135deg, #232946 0%, #181f2a 100%);
            color: var(--text-main);
            font-family: 'Inter', 'Roboto', Arial, sans-serif;
            min-height: 100vh;
            margin: 0;
            display: flex;
            justify-content: center;
            align-items: flex-start;
        }
        .navbar {
            position: fixed;
            top: 0; left: 0; right: 0;
            height: 64px;
            background: rgba(24,31,42,0.95);
            box-shadow: 0 4px 24px rgba(0,0,0,0.18);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 40px;
            z-index: 100;
            backdrop-filter: var(--glass-blur);
        }
        .logo {
            font-size: 1.3rem;
            font-weight: 700;
            color: var(--accent-1);
            letter-spacing: 2px;
            display: flex;
            align-items: center;
        }
        .logo i { margin-right: 10px; }
        .nav-links { display: flex; gap: 18px; }
        .nav-links a, .nav-links button {
            color: var(--text-main);
            background: linear-gradient(90deg, var(--accent-1), var(--accent-3));
            padding: 10px 28px;
            border-radius: 18px;
            font-weight: 600;
            text-decoration: none;
            font-size: 1rem;
            transition: background 0.2s, transform 0.2s;
            border: none;
            cursor: pointer;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .nav-links a.danger {
            background: linear-gradient(90deg, var(--accent-5), #b71c1c);
        }
        .nav-links a:hover, .nav-links button:hover {
            transform: translateY(-2px) scale(1.05);
            filter: brightness(1.2);
        }
        .container {
            max-width: 1600px;
            margin: 0 auto;
            padding: 100px 40px 40px 40px;
            width: 95%;
            display: flex;
            flex-direction: column;
            align-items: center;
        }
        .header {
            width: 100%;
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 40px;
            padding: 30px;
            background: var(--card-bg);
            border-radius: 20px;
            backdrop-filter: var(--glass-blur);
            border: 1px solid rgba(0,188,212,0.12);
        }
        .header-left {
            display: flex;
            align-items: center;
            gap: 20px;
        }
        .header h1 {
            color: var(--accent-1);
            font-size: 24px;
            text-shadow: none;
        }
        .threshold-info {
            color: var(--text-sub);
            font-size: 14px;
            margin-top: 10px;
        }
        .data-table {
            width: 100%;
            border-collapse: separate;
            border-spacing: 0 12px;
            margin: 30px auto;
            background: rgba(0,0,0,0.08);
            border-radius: 12px;
            overflow: hidden;
        }
        .data-table th,
        .data-table td {
            padding: 15px 8px;
            text-align: center;
            vertical-align: middle;
            word-break: break-all;
            white-space: normal;
        }
        .data-table th {
            color: var(--text-sub);
            font-weight: 600;
            border-bottom: 2px solid rgba(0,188,212,0.18);
            font-size: 15px;
            padding: 15px 8px;
        }
        .data-table td {
            background: rgba(0,0,0,0.08);
            color: var(--text-main);
        }
        .data-table tr:hover td {
            background: rgba(0,188,212,0.08);
        }
        .data-table tr td:first-child {
            border-top-left-radius: 8px;
            border-bottom-left-radius: 8px;
        }
        .data-table tr td:last-child {
            border-top-right-radius: 8px;
            border-bottom-right-radius: 8px;
        }
        .pagination {
            display: flex;
            justify-content: center;
            gap: 10px;
            margin-top: 30px;
        }
        .pagination a {
            padding: 8px 16px;
            background: rgba(0,188,212,0.08);
            color: var(--text-main);
            text-decoration: none;
            border-radius: 8px;
            transition: all 0.3s ease;
        }
        .pagination a:hover {
            background: rgba(0,188,212,0.18);
            transform: translateY(-2px);
        }
        .pagination .current {
            background: rgba(0,188,212,0.25);
            color: var(--text-main);
        }
        .action-buttons {
            display: flex;
            gap: 6px;
            flex-wrap: nowrap;
            justify-content: center;
            align-items: center;
            min-width: 260px;
        }
        .btn {
            padding: 6px 12px;
            white-space: nowrap;
            min-width: fit-content;
            font-size: 14px;
            display: inline-flex;
            align-items: center;
            gap: 6px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        .btn-primary {
            background: linear-gradient(135deg, var(--accent-1), var(--accent-4));
            color: white;
        }
        .btn-warning {
            background: linear-gradient(135deg, var(--accent-2), var(--accent-6));
            color: white;
        }
        .btn-danger {
            background: linear-gradient(135deg, var(--accent-5), #b71c1c);
            color: white;
        }
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
        }
        .modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            backdrop-filter: blur(5px);
            z-index: 1000;
        }
        .modal-content {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: var(--card-bg);
            padding: 30px;
            border-radius: 15px;
            min-width: 400px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
            color: var(--text-main);
        }
        .modal-header {
            margin-bottom: 20px;
        }
        .modal-header h2 {
            color: var(--accent-1);
            font-size: 20px;
        }
        .form-group {
            margin-bottom: 15px;
        }
        .form-group label {
            display: block;
            margin-bottom: 5px;
            color: var(--text-sub);
        }
        .form-group input,
        .form-group select {
            width: 100%;
            padding: 10px;
            border: 1.5px solid var(--accent-1);
            border-radius: 8px;
            font-size: 14px;
            background: rgba(30,41,59,0.7);
            color: var(--text-main);
        }
        .form-group input:focus,
        .form-group select:focus {
            outline: none;
            border-color: var(--accent-2);
            box-shadow: 0 0 5px var(--accent-2);
        }
        .modal-footer {
            display: flex;
            justify-content: flex-end;
            gap: 10px;
            margin-top: 20px;
        }
        .close {
            position: absolute;
            top: 15px;
            right: 15px;
            font-size: 24px;
            color: var(--text-sub);
            cursor: pointer;
        }
        .close:hover {
            color: var(--accent-1);
        }
        .threshold-info {
            color: var(--text-sub);
            font-size: 14px;
            margin-top: 10px;
        }
        .abnormal-order td {
            background: #f8f9fa !important;
        }
        .status {
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 500;
        }
        .status.warning {
            background: rgba(255, 152, 0, 0.2);
            color: #FFA000;
        }
        .status.verified {
            background: rgba(76, 175, 80, 0.2);
            color: #4CAF50;
        }
        textarea {
            width: 100%;
            padding: 10px;
            border: 1.5px solid var(--accent-1);
            border-radius: 8px;
            font-size: 14px;
            resize: vertical;
            background: rgba(30,41,59,0.7);
            color: var(--text-main);
        }
        textarea:focus {
            outline: none;
            border-color: var(--accent-2);
            box-shadow: 0 0 5px var(--accent-2);
        }
        /* 设置各列的宽度 */
        .data-table th:nth-child(1),
        .data-table td:nth-child(1) { width: 8em; }  /* 订单ID */
        .data-table th:nth-child(2),
        .data-table td:nth-child(2) { width: 6em; }   /* 日期 */
        .data-table th:nth-child(3),
        .data-table td:nth-child(3) { width: 5em; }   /* 时间 */
        .data-table th:nth-child(5),
        .data-table td:nth-child(5) { width: 4em; }   /* 数量 */
        .data-table th:nth-child(6),
        .data-table td:nth-child(6) { width: 6em; }   /* 原价 */
        .data-table th:nth-child(7),
        .data-table td:nth-child(7) { width: 7em; }   /* 实付金额 */
        .data-table th:nth-child(10),
        .data-table td:nth-child(10) { width: 5em; }  /* 折扣率 */
        .data-table th:nth-child(12),
        .data-table td:nth-child(12) { 
            min-width: 180px;
        }  /* 操作按钮 */
        @media (max-width: 1400px) {
            .container {
                width: 95%;
                padding: 10px 20px;
            }
            .data-table th,
            .data-table td {
                padding: 12px 8px;
                font-size: 13px;
            }
            .btn {
                padding: 4px 8px;
                font-size: 12px;
            }
            .btn i {
                font-size: 14px;
            }
        }
        @media (max-width: 900px) {
            .container { padding: 100px 4px 4px 4px; }
        }
        @media (max-width: 768px) {
            .data-table { font-size: 14px; }
            .data-table th, .data-table td { padding: 10px; }
            .modal-content { min-width: 90vw; }
        }
    </style>
</head>
<body>
    <nav class="navbar">
        <div class="logo"><i class="fas fa-mug-hot"></i> 基于Python的咖啡销售数据分析系统</div>
        <div class="nav-links">
            <a href="/">首页</a>
            <a href="/predict/">返回预测</a>
            <a href="/users/">用户管理</a>
            <a href="/metrics/">模型指标</a>
            <button class="btn btn-primary" onclick="showCreateOrderModal()">新建订单</button>
            <a href="/logout/" class="danger">退出登录</a>
        </div>
    </nav>
    <div class="container">
        <div class="header">
            <div class="header-left">
                <h1>数据管理</h1>
                <div class="threshold-info">
                    <span>异常订单阈值: {{ threshold }} (平均值: {{ avg_quantity }}, 标准差: {{ std_quantity }})</span>
                </div>
            </div>
            <!-- <div class="nav-links">
                <a href="/" class="primary">
                    <i class="mdi mdi-home"></i>返回首页
                </a>
                <a href="/predict/" class="primary">
                    <i class="mdi mdi-chart-line"></i>返回预测
                </a>
                <a href="/users/" class="primary">
                    <i class="mdi mdi-account-group"></i>用户管理
                </a>
                <a href="/metrics/" class="primary">
                    <i class="mdi mdi-chart-box"></i>模型指标
                </a>
                <button class="btn btn-primary" onclick="showCreateOrderModal()">
                    <i class="mdi mdi-plus"></i>新建订单
                </button>
                <a href="/logout/" class="danger">
                    <i class="mdi mdi-logout"></i>退出登录
                </a>
            </div> -->
        </div>

        <table class="data-table">
            <thead>
                <tr>
                    <th>订单ID</th>
                    <th>日期</th>
                    <th>时间</th>
                    <th>产品名称</th>
                    <th>数量</th>
                    <th>原价</th>
                    <th>实付金额</th>
                    <th>支付方式</th>
                    <th>会员等级</th>
                    <th>折扣率</th>
                    <th>操作</th>
                </tr>
            </thead>
            <tbody>
                {% for order in orders %}
                <tr {% if order.is_abnormal %}class="abnormal-order"{% endif %}>
                    <td>{{ order.order_id }}</td>
                    <td>{{ order.order_date|date:"Y-m-d" }}</td>
                    <td>{{ order.order_time|time:"H:i:s" }}</td>
                    <td>{{ order.product_name }}</td>
                    <td>{{ order.quantity }}</td>
                    <td>¥{{ order.original_price|floatformat:2 }}</td>
                    <td>¥{{ order.paid_amount|floatformat:2 }}</td>
                    <td>{{ order.payment_method }}</td>
                    <td>{{ order.member_level }}</td>
                    <td>{{ order.discount_rate|floatformat:2 }}</td>
                    <td class="action-buttons">
                        <button class="btn btn-warning edit-btn" 
                            data-order-id="{{ order.order_id }}"
                            data-order-date="{{ order.order_date|date:'Y-m-d' }}"
                            data-order-time="{{ order.order_time|time:'H:i:s' }}"
                            data-product-name="{{ order.product_name }}"
                            data-quantity="{{ order.quantity }}"
                            data-original-price="{{ order.original_price }}"
                            data-paid-amount="{{ order.paid_amount }}"
                            data-payment-method="{{ order.payment_method }}"
                            data-member-level="{{ order.member_level }}"
                            data-discount-rate="{{ order.discount_rate }}"
                            onclick="handleEditClick(this)">
                            <i class="mdi mdi-pencil"></i>编辑
                        </button>
                        <button class="btn btn-danger" onclick="deleteOrder('{{ order.order_id }}')">
                            <i class="mdi mdi-delete"></i>删除
                        </button>
                    </td>
                </tr>
                {% endfor %}
            </tbody>
        </table>

        <div class="pagination">
            {% if orders.has_previous %}
                <a href="?page={{ orders.previous_page_number }}">上一页</a>
            {% endif %}
            
            <a class="current">{{ orders.number }} / {{ orders.paginator.num_pages }}</a>
            
            {% if orders.has_next %}
                <a href="?page={{ orders.next_page_number }}">下一页</a>
            {% endif %}
        </div>
    </div>

    <!-- 创建订单模态框 -->
    <div id="createOrderModal" class="modal">
        <div class="modal-content">
            <span class="close" onclick="hideModal('createOrderModal')">&times;</span>
            <div class="modal-header">
                <h2>创建新订单</h2>
            </div>
            <form id="createOrderForm" onsubmit="createOrder(event)">
                <div class="form-group">
                    <label>订单ID</label>
                    <input type="text" name="order_id" required>
                </div>
                <div class="form-group">
                    <label>日期</label>
                    <input type="date" name="order_date" required>
                </div>
                <div class="form-group">
                    <label>时间</label>
                    <input type="time" name="order_time" required>
                </div>
                <div class="form-group">
                    <label>产品名称</label>
                    <input type="text" name="product_name" required>
                </div>
                <div class="form-group">
                    <label>数量</label>
                    <input type="number" name="quantity" min="1" required>
                </div>
                <div class="form-group">
                    <label>原价</label>
                    <input type="number" name="original_price" min="0" step="0.01" required>
                </div>
                <div class="form-group">
                    <label>实付金额</label>
                    <input type="number" name="paid_amount" min="0" step="0.01" required>
                </div>
                <div class="form-group">
                    <label>支付方式</label>
                    <select name="payment_method" required>
                        <option value="现金">现金</option>
                        <option value="微信">微信</option>
                        <option value="支付宝">支付宝</option>
                        <option value="银行卡">银行卡</option>
                    </select>
                </div>
                <div class="form-group">
                    <label>会员等级</label>
                    <select name="member_level" required>
                        <option value="普通会员">普通会员</option>
                        <option value="白银会员">白银会员</option>
                        <option value="黄金会员">黄金会员</option>
                        <option value="铂金会员">铂金会员</option>
                        <option value="钻石会员">钻石会员</option>
                    </select>
                </div>
                <div class="form-group">
                    <label>折扣率</label>
                    <input type="number" name="discount_rate" min="0" max="1" step="0.01" required>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn" onclick="hideModal('createOrderModal')">取消</button>
                    <button type="submit" class="btn btn-primary">创建</button>
                </div>
            </form>
        </div>
    </div>

    <!-- 编辑订单模态框 -->
    <div id="editOrderModal" class="modal">
        <div class="modal-content">
            <span class="close" onclick="hideModal('editOrderModal')">&times;</span>
            <div class="modal-header">
                <h2>编辑订单</h2>
            </div>
            <form id="editOrderForm" onsubmit="updateOrder(event)">
                <input type="hidden" name="order_id">
                <div class="form-group">
                    <label>日期</label>
                    <input type="date" name="order_date" required>
                </div>
                <div class="form-group">
                    <label>时间</label>
                    <input type="text" name="order_time" required>
                </div>
                <div class="form-group">
                    <label>产品名称</label>
                    <input type="text" name="product_name" required>
                </div>
                <div class="form-group">
                    <label>数量</label>
                    <input type="number" name="quantity" min="1" required>
                </div>
                <div class="form-group">
                    <label>原价</label>
                    <input type="number" name="original_price" min="0" step="0.01" required>
                </div>
                <div class="form-group">
                    <label>实付金额</label>
                    <input type="number" name="paid_amount" min="0" step="0.01" required>
                </div>
                <div class="form-group">
                    <label>支付方式</label>
                    <select name="payment_method" required>
                        <option value="现金">现金</option>
                        <option value="微信">微信</option>
                        <option value="支付宝">支付宝</option>
                        <option value="银行卡">银行卡</option>
                    </select>
                </div>
                <div class="form-group">
                    <label>会员等级</label>
                    <select name="member_level" required>
                        <option value="普通会员">普通会员</option>
                        <option value="白银会员">白银会员</option>
                        <option value="黄金会员">黄金会员</option>
                        <option value="铂金会员">铂金会员</option>
                        <option value="钻石会员">钻石会员</option>
                    </select>
                </div>
                <div class="form-group">
                    <label>折扣率</label>
                    <input type="number" name="discount_rate" min="0" max="1" step="0.01" required>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn" onclick="hideModal('editOrderModal')">取消</button>
                    <button type="submit" class="btn btn-primary">保存</button>
                </div>
            </form>
        </div>
    </div>

    <!-- 验证订单模态框 -->
    <div id="verifyOrderModal" class="modal">
        <div class="modal-content">
            <span class="close" onclick="hideModal('verifyOrderModal')">&times;</span>
            <div class="modal-header">
                <h2>验证异常订单</h2>
            </div>
            <form id="verifyOrderForm" onsubmit="verifyOrder(event)">
                <input type="hidden" name="order_id">
                <div class="form-group">
                    <label>验证结果</label>
                    <select name="is_verified" required>
                        <option value="true">确认为正常订单</option>
                        <option value="false">标记为异常订单</option>
                    </select>
                </div>
                <div class="form-group">
                    <label>备注说明</label>
                    <textarea name="verification_note" rows="3" placeholder="请输入验证说明..."></textarea>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn" onclick="hideModal('verifyOrderModal')">取消</button>
                    <button type="submit" class="btn btn-primary">确认</button>
                </div>
            </form>
        </div>
    </div>

    <script>
        function getCookie(name) {
            let cookieValue = null;
            if (document.cookie && document.cookie !== '') {
                const cookies = document.cookie.split(';');
                for (let i = 0; i < cookies.length; i++) {
                    const cookie = cookies[i].trim();
                    if (cookie.substring(0, name.length + 1) === (name + '=')) {
                        cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
                        break;
                    }
                }
            }
            return cookieValue;
        }

        function showToast(message, type = 'success') {
            const toast = document.createElement('div');
            toast.className = `toast ${type}`;
            toast.innerHTML = `
                <i class="mdi mdi-${type === 'success' ? 'check-circle' : 'alert-circle'}"></i>
                ${message}
            `;
            document.body.appendChild(toast);
            
            setTimeout(() => {
                toast.remove();
            }, 3000);
        }

        function showModal(id) {
            document.getElementById(id).style.display = 'block';
        }

        function hideModal(id) {
            document.getElementById(id).style.display = 'none';
        }

        function showCreateOrderModal() {
            document.getElementById('createOrderForm').reset();
            showModal('createOrderModal');
        }

        function handleEditClick(button) {
            const data = button.dataset;
            showEditOrderModal(
                data.orderId,
                data.orderDate,
                data.orderTime,
                data.productName,
                parseFloat(data.quantity),
                parseFloat(data.originalPrice),
                parseFloat(data.paidAmount),
                data.paymentMethod,
                data.memberLevel,
                parseFloat(data.discountRate)
            );
        }

        function showEditOrderModal(orderId, orderDate, orderTime, productName, quantity, originalPrice, paidAmount, paymentMethod, memberLevel, discountRate) {
            const form = document.getElementById('editOrderForm');
            form.reset();
            form.order_id.value = orderId;
            form.order_date.value = orderDate;
            form.order_time.value = orderTime || '';
            form.product_name.value = productName;
            form.quantity.value = quantity;
            form.original_price.value = originalPrice;
            form.paid_amount.value = paidAmount;
            form.payment_method.value = paymentMethod;
            form.member_level.value = memberLevel;
            form.discount_rate.value = discountRate;
            showModal('editOrderModal');
        }

        async function createOrder(event) {
            event.preventDefault();
            const form = event.target;
            const data = {
                order_id: form.order_id.value,
                order_date: form.order_date.value,
                order_time: form.order_time.value,
                product_name: form.product_name.value,
                quantity: parseInt(form.quantity.value),
                original_price: parseFloat(form.original_price.value),
                paid_amount: parseFloat(form.paid_amount.value),
                payment_method: form.payment_method.value,
                member_level: form.member_level.value,
                discount_rate: parseFloat(form.discount_rate.value)
            };

            try {
                const response = await fetch('/api/orders/create/', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRFToken': getCookie('csrftoken')
                    },
                    body: JSON.stringify(data)
                });

                const result = await response.json();
                if (result.success) {
                    showToast('订单创建成功');
                    hideModal('createOrderModal');
                    location.reload();
                } else {
                    showToast(result.error, 'error');
                }
            } catch (error) {
                showToast('创建订单失败', 'error');
            }
        }

        async function updateOrder(event) {
            event.preventDefault();
            const form = event.target;
            const orderId = form.order_id.value;
            
            // 处理时间格式
            let orderTime = form.order_time.value;
            if (orderTime) {
                // 确保时间格式为 HH:MM:SS
                const timeParts = orderTime.split(':');
                if (timeParts.length === 2) {
                    orderTime = `${orderTime}:00`;
                }
            }
            
            const data = {
                order_date: form.order_date.value,
                order_time: orderTime,
                product_name: form.product_name.value,
                quantity: parseInt(form.quantity.value),
                original_price: parseFloat(form.original_price.value),
                paid_amount: parseFloat(form.paid_amount.value),
                payment_method: form.payment_method.value,
                member_level: form.member_level.value,
                discount_rate: parseFloat(form.discount_rate.value)
            };

            try {
                const response = await fetch(`/api/orders/${orderId}/update/`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRFToken': getCookie('csrftoken')
                    },
                    body: JSON.stringify(data)
                });

                const result = await response.json();
                if (result.success) {
                    showToast('订单更新成功');
                    hideModal('editOrderModal');
                    location.reload();
                } else {
                    showToast(result.error, 'error');
                }
            } catch (error) {
                showToast('更新订单失败', 'error');
            }
        }

        async function deleteOrder(orderId) {
            if (!confirm(`确定要删除订单 "${orderId}" 吗？`)) {
                return;
            }

            try {
                const response = await fetch(`/api/orders/${orderId}/delete/`, {
                    method: 'POST',
                    headers: {
                        'X-CSRFToken': getCookie('csrftoken')
                    }
                });

                const result = await response.json();
                if (result.success) {
                    showToast('订单删除成功');
                    location.reload();
                } else {
                    showToast(result.error, 'error');
                }
            } catch (error) {
                showToast('删除订单失败', 'error');
            }
        }

        function showVerifyOrderModal(orderId) {
            const form = document.getElementById('verifyOrderForm');
            form.reset();
            form.order_id.value = orderId;
            showModal('verifyOrderModal');
        }

        async function verifyOrder(event) {
            event.preventDefault();
            const form = event.target;
            const orderId = form.order_id.value;
            const data = {
                is_verified: form.is_verified.value === 'true',
                verification_note: form.verification_note.value
            };

            try {
                const response = await fetch(`/api/orders/${orderId}/verify/`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRFToken': getCookie('csrftoken')
                    },
                    body: JSON.stringify(data)
                });

                const result = await response.json();
                if (result.success) {
                    if (result.data.deleted) {
                        showToast('异常订单已删除');
                    } else {
                        showToast('订单验证成功');
                    }
                    hideModal('verifyOrderModal');
                    location.reload();
                } else {
                    showToast(result.error, 'error');
                }
            } catch (error) {
                showToast('验证订单失败', 'error');
            }
        }
    </script>
</body>
</html> 