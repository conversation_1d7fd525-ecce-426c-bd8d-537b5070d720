<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>基于Python的咖啡销售数据分析系统设计与实现 - 登录</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
    <link href="https://fonts.googleapis.com/css?family=Inter:400,600,700&display=swap" rel="stylesheet">
    <style>
        :root {
            --primary-bg: #181f2a;
            --card-bg: rgba(30, 41, 59, 0.85);
            --accent-1: #00bcd4;
            --accent-2: #ff9800;
            --accent-3: #8e24aa;
            --accent-4: #43a047;
            --accent-5: #f44336;
            --accent-6: #ffd600;
            --text-main: #fff;
            --text-sub: #b0bec5;
            --glass-blur: blur(12px);
        }
        body {
            background: linear-gradient(135deg, #232946 0%, #181f2a 100%);
            color: var(--text-main);
            font-family: 'Inter', 'Roboto', Arial, sans-serif;
            min-height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
            position: relative;
            overflow: hidden;
        }
        .stars { position: fixed; top: 0; left: 0; width: 100%; height: 100%; pointer-events: none; z-index: 0; }
        .star { position: absolute; width: 2px; height: 2px; background: #fff; border-radius: 50%; animation: twinkle var(--duration) linear infinite; box-shadow: 0 0 4px #fff; }
        @keyframes twinkle { 0% { opacity: 1; transform: scale(1); } 50% { opacity: 0.3; transform: scale(0.7); } 100% { opacity: 1; transform: scale(1); } }
        .meteor { position: absolute; width: 3px; height: 3px; background: linear-gradient(to right, #ffffff, transparent); animation: meteor 2s linear infinite; transform: rotate(-45deg); box-shadow: 0 0 10px #fff; }
        @keyframes meteor { 0% { transform: translateX(0) translateY(0) rotate(-45deg); opacity: 1; } 70% { opacity: 1; } 100% { transform: translateX(-500px) translateY(500px) rotate(-45deg); opacity: 0; } }
        .navbar {
            position: fixed;
            top: 0; left: 0; right: 0;
            height: 64px;
            background: rgba(24,31,42,0.95);
            box-shadow: 0 4px 24px rgba(0,0,0,0.18);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 40px;
            z-index: 10;
            backdrop-filter: var(--glass-blur);
        }
        .logo {
            font-size: 1.3rem;
            font-weight: 700;
            color: var(--accent-1);
            letter-spacing: 2px;
            display: flex;
            align-items: center;
        }
        .logo i { margin-right: 10px; }
        .nav-links { display: flex; gap: 18px; }
        .nav-links a {
            color: var(--text-main);
            background: linear-gradient(90deg, var(--accent-1), var(--accent-3));
            padding: 10px 28px;
            border-radius: 18px;
            font-weight: 600;
            text-decoration: none;
            font-size: 1rem;
            transition: background 0.2s, transform 0.2s;
        }
        .nav-links a.danger {
            background: linear-gradient(90deg, var(--accent-5), #b71c1c);
        }
        .nav-links a:hover {
            transform: translateY(-2px) scale(1.05);
            filter: brightness(1.2);
        }
        .login-container {
            background: var(--card-bg);
            padding: 40px;
            border-radius: 24px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            border: 1px solid rgba(0,188,212,0.12);
            width: 100%;
            max-width: 420px;
            position: relative;
            z-index: 1;
            transform: translateY(0);
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            backdrop-filter: var(--glass-blur);
        }
        .login-container:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 45px rgba(0, 188, 212, 0.15);
        }
        .login-title {
            text-align: center;
            margin-bottom: 35px;
            color: var(--accent-1);
            font-size: 32px;
            font-weight: bold;
            position: relative;
            letter-spacing: 1px;
        }
        .login-title::after {
            content: '';
            position: absolute;
            bottom: -12px;
            left: 50%;
            transform: translateX(-50%);
            width: 60px;
            height: 4px;
            background: linear-gradient(90deg, var(--accent-1), var(--accent-2));
            border-radius: 4px;
        }
        .form-group {
            margin-bottom: 28px;
            position: relative;
        }
        .form-group label {
            display: block;
            margin-bottom: 10px;
            color: var(--text-sub);
            font-size: 15px;
            font-weight: 500;
            transition: all 0.3s ease;
        }
        .form-group input {
            width: 90%;
            padding: 15px 20px;
            background: rgba(30,41,59,0.7);
            border: 2px solid var(--accent-1);
            border-radius: 12px;
            font-size: 15px;
            color: var(--text-main);
            transition: all 0.3s ease;
            letter-spacing: 0.5px;
        }
        .form-group input:focus {
            outline: none;
            border-color: var(--accent-2);
            box-shadow: 0 0 20px var(--accent-2);
            background: rgba(30,41,59,0.9);
        }
        .form-group input::placeholder {
            color: #b0bec5;
        }
        .btn {
            width: 100%;
            padding: 16px;
            background: linear-gradient(135deg, var(--accent-1), var(--accent-2));
            color: white;
            border: none;
            border-radius: 12px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            overflow: hidden;
            letter-spacing: 1px;
        }
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px var(--accent-1);
            background: linear-gradient(135deg, var(--accent-2), var(--accent-1));
        }
        .message {
            margin: 20px 0;
            padding: 12px 20px;
            border-radius: 12px;
            text-align: center;
            font-size: 14px;
            background: rgba(0,188,212,0.08);
            color: var(--accent-1);
            border: 1px solid rgba(0,188,212,0.12);
        }
        .register-link {
            margin-top: 30px;
            text-align: center;
            font-size: 15px;
            color: var(--text-sub);
        }
        .register-link a {
            color: var(--accent-1);
            text-decoration: none;
            font-weight: 500;
            margin-left: 8px;
            position: relative;
            transition: all 0.3s ease;
        }
        .register-link a::after {
            content: '';
            position: absolute;
            width: 100%;
            height: 2px;
            bottom: -3px;
            left: 0;
            background: linear-gradient(90deg, var(--accent-2), var(--accent-1));
            transform: scaleX(0);
            transition: transform 0.3s ease;
        }
        .register-link a:hover {
            color: var(--accent-2);
            text-shadow: 0 0 10px var(--accent-1);
        }
        .register-link a:hover::after {
            transform: scaleX(1);
        }
        @media (max-width: 480px) {
            .login-container { margin: 20px; padding: 30px; }
            .login-title { font-size: 28px; }
            .form-group input { padding: 12px 15px; }
            .btn { padding: 14px; }
        }
    </style>
</head>
<body>
    <nav class="navbar">
        <div class="logo"><i class="fas fa-mug-hot"></i> 基于Python的咖啡销售数据分析系统</div>
        <div class="nav-links">
            <a href="/">首页</a>
            <a href="/register/">注册</a>
        </div>
    </nav>
    <div class="stars"></div>
    <div class="login-container">
        <h1 class="login-title">用户登录</h1>
        {% if messages %}
        {% for message in messages %}
        <div class="message {% if message.tags %}{{ message.tags }}{% endif %}">
            {{ message }}
        </div>
        {% endfor %}
        {% endif %}
        <form method="post">
            {% csrf_token %}
            <div class="form-group">
                <label for="username">用户名</label>
                <input type="text" id="username" name="username" placeholder="请输入用户名" required>
            </div>
            <div class="form-group">
                <label for="password">密码</label>
                <input type="password" id="password" name="password" placeholder="请输入密码" required>
            </div>
            <button type="submit" class="btn">登 录</button>
        </form>
        <div class="register-link">
            还没有账号？<a href="{% url 'register' %}">立即注册</a>
        </div>
    </div>

    <script>
        // 创建星星背景
        function createStars() {
            const stars = document.querySelector('.stars');
            const count = 200;
            
            for(let i = 0; i < count; i++) {
                const star = document.createElement('div');
                star.className = 'star';
                
                // 随机位置
                star.style.left = `${Math.random() * 100}%`;
                star.style.top = `${Math.random() * 100}%`;
                
                // 随机大小
                const size = Math.random() * 3;
                star.style.width = `${size}px`;
                star.style.height = `${size}px`;
                
                // 随机动画持续时间
                star.style.setProperty('--duration', `${2 + Math.random() * 3}s`);
                
                stars.appendChild(star);
            }
        }

        // 创建流星
        function createMeteor() {
            const stars = document.querySelector('.stars');
            const meteor = document.createElement('div');
            meteor.className = 'meteor';
            
            // 随机起始位置
            meteor.style.left = `${Math.random() * 100 + 50}%`;
            meteor.style.top = '0';
            
            stars.appendChild(meteor);
            
            // 动画结束后移除流星
            setTimeout(() => {
                meteor.remove();
            }, 2000);
        }

        // 初始化
        createStars();
        
        // 定期创建流星
        setInterval(createMeteor, 3000);
    </script>
</body>
</html> 