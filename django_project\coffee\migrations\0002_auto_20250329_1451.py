# Generated by Django 3.2.25 on 2025-03-29 06:51

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('coffee', '0001_initial'),
    ]

    operations = [
        migrations.AlterModelOptions(
            name='coffeeorder',
            options={'ordering': ['-order_date', '-order_time'], 'verbose_name': '咖啡订单', 'verbose_name_plural': '咖啡订单'},
        ),
        migrations.AddField(
            model_name='coffeeorder',
            name='is_verified',
            field=models.BooleanField(default=False),
        ),
        migrations.AddField(
            model_name='coffeeorder',
            name='verification_note',
            field=models.TextField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='coffeeorder',
            name='verified_at',
            field=models.DateTimeField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='coffeeorder',
            name='verified_by',
            field=models.CharField(blank=True, max_length=100, null=True),
        ),
        migrations.AlterField(
            model_name='coffeeorder',
            name='discount_rate',
            field=models.DecimalField(decimal_places=2, max_digits=4, verbose_name='折扣率'),
        ),
        migrations.AlterField(
            model_name='coffeeorder',
            name='member_level',
            field=models.CharField(max_length=50, verbose_name='会员等级'),
        ),
        migrations.AlterField(
            model_name='coffeeorder',
            name='order_id',
            field=models.CharField(max_length=50, primary_key=True, serialize=False, verbose_name='订单ID'),
        ),
        migrations.AlterField(
            model_name='coffeeorder',
            name='payment_method',
            field=models.CharField(max_length=50, verbose_name='支付方式'),
        ),
        migrations.AlterField(
            model_name='coffeeorder',
            name='product_name',
            field=models.CharField(max_length=100, verbose_name='产品名称'),
        ),
    ]
